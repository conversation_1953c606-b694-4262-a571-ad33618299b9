import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { CustomLogger } from './common/services/logger.service';
import { RuntimeExceptionFilter } from './middlewares';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as path from 'path';
import { BootstrapService } from './bootstrap.service';
import { NetworkService } from './utils/services/network.service';
import { Response } from 'express';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: ['log', 'error', 'debug', 'warn'],
  });

  app.use(bodyParser.json({ limit: '5mb' }));
  app.use(bodyParser.urlencoded({ limit: '5mb', extended: true }));

  app.setGlobalPrefix('api');

  const corsOptions: CorsOptions = {
    origin: [process.env.FRONTEND_URL],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  };
  app.enableCors(corsOptions);

  app.useLogger(app.get(CustomLogger));

  app.useGlobalFilters(new RuntimeExceptionFilter(app.get(CustomLogger)));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      validateCustomDecorators: true, // ensures custom decorators are validated
    }),
  );

  const options = new DocumentBuilder()
    .setTitle('Appetec API Documentation')
    .setDescription('API for managing Appetec platform resources.')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, options);

  SwaggerModule.setup('/api/swagger', app, document);

  app.setViewEngine('ejs');
  app.setBaseViewsDir(path.join(__dirname, '..', '..', 'views'));

  await app.get(BootstrapService).createAdmin();

  // welcome page for the root URL
  app.getHttpAdapter().get('/', (req, res: Response) => {
    res.status(200).send('Welcome to Appetec API');
  });

  const PORT = parseInt(process.env.SERVER_PORT) || 4000;

  const networkService = app.get(NetworkService);
  const IP = networkService.getServerIp();

  if (IP) {
    await app.listen(PORT, IP);

    console.log(`Application is running on http://${IP}:${PORT}`);
  } else {
    await app.listen(PORT);

    console.log(`Application is running on http://localhost:${PORT}`);
  }
}

bootstrap();
