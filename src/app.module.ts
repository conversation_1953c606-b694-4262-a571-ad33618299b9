import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { ServeStaticModule } from '@nestjs/serve-static';
import { User, UserSchema } from 'models/user';
import { join } from 'path';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user/user.module';
import { GoalsModule } from './user/goals/goals.module';
import { ThirdPartyModule } from './third-party/third-party.module';
import { CommonModule } from './common/common.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from './repo/repo.module';
import { UserDeviceModule } from './user/device/device.module';
import { UserRecordsModule } from './user/user-records/user-records.module';
import { AdminModule } from './admin/admin.module';
import { ContactUsModule } from './user/contact_us/contact_us.module';
import { BootstrapService } from './bootstrap.service';
import { NetworkService } from './utils/services/network.service';
import { AppController } from './app.controller';
import { APIUrlLoggerMiddleware } from './middlewares';
import { DatabaseConfigService } from 'config';
import { VideoLibraryModule } from './user/video-library/video-library.module';
import { AppPermissionsModule } from './user/app-permissions/app-permissions.module';
import { FaqModule } from './user/faq/faq.module';
import { HelpModule } from './user/help/help.module';
import { DeviceModule } from './user/device/getAllDevicesToUser/getAllDeviceToUser.module';
import { RemindersModule } from './user/reminders/reminders.module';
import { ScheduleModule } from '@nestjs/schedule';
import { NotificationTokenModule } from './user/notification/notificationtoken.module';
import { RecipeModule } from './user/recipe/recipe.module';
import { CronjobsModule } from './cronjobs/cronjobs.module';
import { UserHealthModule } from './user/user-health/user-health.module';
import { UserHighlightsModule } from './user/user-highlights/user-highlights.module';
import { ChatbotModule } from './chatbot/chatbot.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { IngredientModule } from './user/ingredient/ingredient.module';
import { CarouselModule } from './user/carousel/carousel.module';
import { MoodAndHungerTypeModule } from './user/moodandhungertype/moodandhungertype.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useClass: DatabaseConfigService,
      inject: [ConfigService],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '../../public'),
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 10,
        },
      ],
    }), // rate
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    MulterModule.register(),

    CronjobsModule,
    RedisModule, // redis connection will be called from here
    ThirdPartyModule,
    RepoModule,
    CommonModule,

    AuthModule,
    UserModule,
    UserRecordsModule,
    GoalsModule,
    DeviceModule,
    ContactUsModule,
    VideoLibraryModule,
    AppPermissionsModule,
    FaqModule,
    HelpModule,
    UserDeviceModule,
    RemindersModule,
    RecipeModule,

    AdminModule,
    ChatbotModule,
    NotificationTokenModule,
    UserHealthModule,
    UserHighlightsModule,
    IngredientModule,
    CarouselModule,
    MoodAndHungerTypeModule,
  ],
  providers: [BootstrapService, NetworkService],
  controllers: [AppController],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(APIUrlLoggerMiddleware).forRoutes('*');
  }
}
