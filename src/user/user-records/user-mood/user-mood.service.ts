import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { UserMoodRecords } from 'models/user-records/Mood-Records';
import { Model } from 'mongoose';
import {
  CreateUserMoodRecordsReqDTO,
  CreateUserMoodRecordsResDTO,
  GetSingleUserMoodRecordsResDTO,
  GetUserMoodAvgRecordDTO,
  GetUserMoodRecordDTO,
  UpdateMoodRecordReqDto,
  UpdateMoodRecordResDto,
  UserMoodRecordDTO,
} from './user-mood-record-dto';

import { UserMoodUtilsService } from './user-mood-utils.service';
import {
  AnalyticsRecordFilter,
  GetUserAnalyticsQueryInterface,
} from 'src/common/interfaces';
import { User } from 'models/user';
import * as moment from 'moment-timezone';

@Injectable()
export class UserMoodService {
  constructor(
    @InjectModel(UserMoodRecords.name)
    private readonly userMoodRecordsModel: Model<UserMoodRecords>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    private readonly userMoodUtilsService: UserMoodUtilsService,
  ) {}

  async createMoodRecord(
    userId: string,
    recordData: CreateUserMoodRecordsReqDTO,
  ): Promise<CreateUserMoodRecordsResDTO> {
    const { moodType, hungerLevel } = recordData;

    // Get timezone-aware current datetime using date-fns-tz
    const now =
      await this.userMoodUtilsService.getUserTimezoneAdjustedNow(userId);

    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour earlier

    // Duplicate check in past hour (user's local time)
    const existingRecord = await this.userMoodRecordsModel.findOne({
      userId,
      createdAt: {
        $gte: oneHourAgo,
        $lte: now,
      },
    });

    if (existingRecord) {
      throw new BadRequestException(
        'A mood entry has already been recorded for this hour. Please try again later.',
      );
    }

    // Create mood record with user's local timestamp
    const newRecord = await this.userMoodRecordsModel.create({
      userId,
      moodType,
      hungerLevel,
    });

    // Update daily and monthly averages using the same timezone-adjusted timestamp
    const utcNow = new Date();
    console.log('utcNow', utcNow);
    await this.userMoodUtilsService.updateDailyMoodAverage(userId, utcNow);
    await this.userMoodUtilsService.updateMonthlyMoodAverage(userId, utcNow);

    const moodRecordResp = UserMoodRecordDTO.transform(newRecord);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Mood Record Added Successfully !!',
      record: moodRecordResp,
    };
  }

  async getUserMoodAnalytics(
    queryFilters: GetUserAnalyticsQueryInterface,
    userId: string,
  ): Promise<GetUserMoodRecordDTO | GetUserMoodAvgRecordDTO> {
    const { filter } = queryFilters;

    const currentDate = new Date();
    currentDate.setHours(23, 59, 59, 999);

    switch (filter) {
      case AnalyticsRecordFilter.WEEKLY:
        return this.userMoodUtilsService.getWeeklyMoodAverages(userId);
      case AnalyticsRecordFilter.MONTHLY:
        return this.userMoodUtilsService.getMonthlyMoodAverages(userId);
      case AnalyticsRecordFilter.HALF_YEARLY:
        return this.userMoodUtilsService.getHalfYearlyMoodAverages(userId);
      case AnalyticsRecordFilter.YEARLY:
        return this.userMoodUtilsService.getYearlyMoodAverages(userId);
      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }

  async updateMoodRecord(
    userId: string,
    id: string,
    updateDto: UpdateMoodRecordReqDto,
  ): Promise<UpdateMoodRecordResDto> {
    const existingRecord = await this.userMoodRecordsModel.findOne({
      _id: id,
      userId: userId,
    });

    if (!existingRecord) {
      throw new NotFoundException('Mood record not found.');
    }

    const updatedRecord = await this.userMoodRecordsModel.findByIdAndUpdate(
      id,
      updateDto,
      {
        new: true,
        runValidators: true,
      },
    );

    const utcNow = new Date();

    // Update daily and monthly mood averages based on user's time zone
    await this.userMoodUtilsService.updateDailyMoodAverage(userId, utcNow);
    await this.userMoodUtilsService.updateMonthlyMoodAverage(userId, utcNow);

    const resp = UserMoodRecordDTO.transform(updatedRecord);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated mood record successfully!',
      data: resp,
    };
  }

  async getUserMoodRecords(
    userId: string,
  ): Promise<GetSingleUserMoodRecordsResDTO> {
    const user = await this.userModel.findById(userId).select('timeZone');
    const userTimeZone = user?.timeZone || 'UTC'; // fallback to UTC if not set

    const userNow = moment().tz(userTimeZone);

    // Get start and end of day in user's time zone
    const startOfDay = userNow.clone().startOf('day').toDate();
    const endOfDay = userNow.clone().endOf('day').toDate();

    let records = await this.userMoodRecordsModel
      .find({ userId, createdAt: { $gte: startOfDay, $lte: endOfDay } })
      .sort({ createdAt: -1 });

    if (records.length === 0) {
      // If no records today, check the last 7 days
      const sevenDaysAgo = userNow
        .clone()
        .subtract(7, 'days')
        .startOf('day')
        .toDate();

      records = await this.userMoodRecordsModel
        .find({ userId, createdAt: { $gte: sevenDaysAgo, $lte: endOfDay } })
        .sort({ createdAt: -1 });

      if (records.length === 0) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          data: null,
        };
      }
    }

    const latestCreatedAt = records[0].createdAt;

    const moodRecords = await this.userMoodRecordsModel
      .find({ userId, createdAt: latestCreatedAt })
      .sort({ createdAt: -1 })
      .exec();

    const resp = UserMoodRecordDTO.transform(moodRecords[0]);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
