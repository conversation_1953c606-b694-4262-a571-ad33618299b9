import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { UserMoodService } from './user-mood.service';
import { ApiTags, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import {
  CreateUserMoodRecordsReqDTO,
  CreateUserMoodRecordsResDTO,
  GetUserMoodAvgRecordDTO,
  GetUserMoodRecordDTO,
  UpdateMoodRecordReqDto,
  UpdateMoodRecordResDto,
} from './user-mood-record-dto';
import { Request } from 'express';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';

import { GetUserAnalyticsQueryInterface } from 'src/common/interfaces';

@ApiTags('User-Mood')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class UserMoodController {
  constructor(private readonly userMoodService: UserMoodService) {}

  @ApiResponse({
    status: 201,
    description: 'Mood record created successfully.',
    type: CreateUserMoodRecordsResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid data or mood record already exists for today.',
    type: ErrorResponse,
  })
  @Post('/mood_records')
  async createMoodRecord(
    @Req() req: Request,
    @Body() createUserMoodRecordsReqDTO: CreateUserMoodRecordsReqDTO,
  ) {
    const user = req['user'];

    return this.userMoodService.createMoodRecord(
      user._id,
      createUserMoodRecordsReqDTO,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'User mood records retrieved successfully.',
    type: GetUserMoodRecordDTO,
  })
  @Get('/mood_analytics')
  async getUserMoodRecords(
    @Query() queryFilters: GetUserAnalyticsQueryInterface,
    @Req() req: Request,
  ): Promise<GetUserMoodRecordDTO | GetUserMoodAvgRecordDTO> {
    const user = req['user'];
    return this.userMoodService.getUserMoodAnalytics(queryFilters, user._id);
  }

  @Get('/mood_records')
  async GetUserMoodRecords(@Req() req: Request) {
    const user = req['user'];
    return this.userMoodService.getUserMoodRecords(user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Mood record updated successfully',
    type: UpdateMoodRecordResDto,
  })
  @Put('/mood_records/:id')
  async updateMoodRecordById(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() updateDto: UpdateMoodRecordReqDto,
  ): Promise<UpdateMoodRecordResDto> {
    const user = req['user'];
    return this.userMoodService.updateMoodRecord(user._id, id, updateDto);
  }
}
