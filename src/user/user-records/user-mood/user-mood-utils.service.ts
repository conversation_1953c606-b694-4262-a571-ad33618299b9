import { Injectable, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  DailyMoodAverage,
  MonthlyMoodAverage,
  UserMoodRecords,
} from 'models/user-records/Mood-Records';

import { Model } from 'mongoose';
import { AverageMoodDTO } from './user-mood-record-dto';
import { User } from 'models/user';
import { toZonedTime } from 'date-fns-tz';
import { format, subDays, addDays } from 'date-fns';

@Injectable()
export class UserMoodUtilsService {
  constructor(
    @InjectModel(UserMoodRecords.name)
    private readonly moodRecordModel: Model<UserMoodRecords>,

    @InjectModel(DailyMoodAverage.name)
    private readonly dailyMoodAverageModel: Model<DailyMoodAverage>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,

    @InjectModel(MonthlyMoodAverage.name)
    private readonly monthlyMoodAverageModel: Model<MonthlyMoodAverage>,
  ) {}

  private readonly moodWeights = {
    happy: 5,
    'moderately happy': 4,
    sad: 3,
    irritated: 2,
    anxious: 1,
  };

  private readonly hungerWeights = {
    high: 4,
    moderate: 3,
    mild: 2,
    barely: 1,
  };

  getScoreLabelMappings() {
    const moodScoreToLabel = Object.fromEntries(
      Object.entries(this.moodWeights).map(([label, score]) => [score, label]),
    );

    const hungerScoreToLabel = Object.fromEntries(
      Object.entries(this.hungerWeights).map(([label, score]) => [
        score,
        label,
      ]),
    );

    return { moodScoreToLabel, hungerScoreToLabel };
  }

  private calculateAverages(records: UserMoodRecords[]): {
    moodTypeScore: number;
    hungerLevelScore: number;
  } {
    const moodSum = records.reduce(
      (sum, r) => sum + (this.moodWeights[r.moodType] || 0),
      0,
    );
    const hungerSum = records.reduce(
      (sum, r) => sum + (this.hungerWeights[r.hungerLevel] || 0),
      0,
    );

    const count = records.length || 1;
    return {
      moodTypeScore: Math.round(moodSum / count),
      hungerLevelScore: Math.round(hungerSum / count),
    };
  }

  private getStartAndEndOfDay(date = new Date()) {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    return { start, end };
  }

  private getDateComponents(date: Date) {
    return {
      date: date.getDate(),
      month: date.getMonth() + 1,
      year: date.getFullYear(),
    };
  }

  async getUserTimezoneAdjustedNow(userId: string): Promise<Date> {
    const user = await this.userModel.findById(userId).select('timeZone');
    const timeZone = user?.timeZone || 'UTC'; // Fallback if not set
    const now = new Date();

    const zonedDate = toZonedTime(now, timeZone); // from date-fns-tz
    return zonedDate;
  }

  async updateDailyMoodAverage(userId: string, now: Date): Promise<void> {
    const { start, end } = this.getStartAndEndOfDay(now);

    const todayRecords = await this.moodRecordModel.find({
      userId,
      createdAt: { $gte: start, $lte: end },
    });

    const { moodTypeScore, hungerLevelScore } =
      this.calculateAverages(todayRecords);

    const { date, month, year } = this.getDateComponents(now);

    await this.dailyMoodAverageModel.findOneAndUpdate(
      { userId, date, month, year },
      { userId, date, month, year, moodTypeScore, hungerLevelScore },
      { upsert: true, new: true },
    );
  }

  async updateMonthlyMoodAverage(userId: string, now: Date): Promise<void> {
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    const dailyAverages = await this.dailyMoodAverageModel.find({
      userId,
      month,
      year,
    });

    if (!dailyAverages.length) return;

    const totalMoodScore = dailyAverages.reduce(
      (sum, record) => sum + (record.moodTypeScore || 0),
      0,
    );
    const totalHungerScore = dailyAverages.reduce(
      (sum, record) => sum + (record.hungerLevelScore || 0),
      0,
    );

    const count = dailyAverages.length || 1;

    const moodTypeScore = Math.round(totalMoodScore / count);
    const hungerLevelScore = Math.round(totalHungerScore / count);

    await this.monthlyMoodAverageModel.findOneAndUpdate(
      { userId, month, year },
      { userId, month, year, moodTypeScore, hungerLevelScore },
      { upsert: true, new: true },
    );
  }

  async getWeeklyMoodAverages(userId: string) {
    const userNow = await this.getUserTimezoneAdjustedNow(userId);
    const user = await this.userModel.findById(userId);
    const timeZone = user?.timeZone || 'UTC';

    // Get start of today in user's local timezone
    const startOfTodayLocal = new Date(userNow);
    startOfTodayLocal.setHours(0, 0, 0, 0);

    // Get UTC equivalent of start and end of today for DB querying
    const startOfTomorrowLocal = new Date(startOfTodayLocal);
    startOfTomorrowLocal.setDate(startOfTomorrowLocal.getDate() + 1);
    const startOfTomorrowUTC = toZonedTime(startOfTomorrowLocal, timeZone);

    // Get UTC start of 7-day window
    const sevenDayWindowStartLocal = subDays(startOfTodayLocal, 6);
    const sevenDayWindowStartUTC = toZonedTime(
      sevenDayWindowStartLocal,
      timeZone,
    );

    // Fetch mood records in user's 7-day window (converted to UTC for DB)
    const records = await this.dailyMoodAverageModel
      .find({
        userId,
        createdAt: {
          $gte: sevenDayWindowStartUTC,
          $lt: startOfTomorrowUTC,
        },
      })
      .lean();

    const groupedData = {};
    records.forEach((record) => {
      const date = new Date(record.year, record.month - 1, record.date);
      const key = format(date, 'yyyy-MM-dd');

      if (!groupedData[key]) groupedData[key] = [];
      groupedData[key].push(record);
    });

    const { moodScoreToLabel, hungerScoreToLabel } =
      this.getScoreLabelMappings();

    // Generate last 7 days in user timezone
    const last7Days: string[] = [];
    for (let i = 6; i >= 0; i--) {
      const localDate = subDays(startOfTodayLocal, i);
      const dateStr = format(localDate, 'yyyy-MM-dd');
      last7Days.push(dateStr);
    }

    const responseRecords = last7Days.map((dateStr) => {
      const dailyRecords = groupedData[dateStr] || [];

      if (dailyRecords.length === 0) {
        return AverageMoodDTO.transform({
          year: dateStr.split('-')[0],
          period: dateStr,
          averageMoodType: null,
          averageHungerLevel: null,
        });
      }

      const averageMood =
        dailyRecords.reduce((sum, r) => sum + (r.moodTypeScore || 0), 0) /
        dailyRecords.length;
      const averageHunger =
        dailyRecords.reduce((sum, r) => sum + (r.hungerLevelScore || 0), 0) /
        dailyRecords.length;

      const roundedMood = Math.round(averageMood);
      const roundedHunger = Math.round(averageHunger);

      return AverageMoodDTO.transform({
        year: dateStr.split('-')[0],
        period: dateStr,
        averageMoodType: moodScoreToLabel[roundedMood] || 'unknown',
        averageHungerLevel: hungerScoreToLabel[roundedHunger] || 'unknown',
      });
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Weekly Mood Averages Fetched Successfully',
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async getMonthlyMoodAverages(userId: string) {
    const userNow = await this.getUserTimezoneAdjustedNow(userId);
    const user = await this.userModel.findById(userId);
    const timeZone = user?.timeZone || 'UTC';

    const endDateLocal = new Date(userNow); // Today
    endDateLocal.setHours(23, 59, 59, 999);
    const startDateLocal = subDays(endDateLocal, 34);
    const startDateUTC = toZonedTime(startDateLocal, timeZone);
    const endDateUTC = toZonedTime(endDateLocal, timeZone);

    // Fetch records in the last 35 days
    const records = await this.dailyMoodAverageModel
      .find({
        userId,
        createdAt: {
          $gte: startDateUTC,
          $lte: endDateUTC,
        },
      })
      .lean();

    const { moodScoreToLabel, hungerScoreToLabel } =
      this.getScoreLabelMappings();

    const responseRecords = [];
    for (let i = 0; i < 5; i++) {
      const weekStartLocal = addDays(startDateLocal, i * 7);
      let weekEndLocal = addDays(weekStartLocal, 6);

      // For the last week, set end date as today
      if (i === 4) {
        weekEndLocal = endDateLocal;
      }

      const weekRecords = records.filter((r) => {
        const createdAt = new Date(r.createdAt);
        return createdAt >= weekStartLocal && createdAt <= weekEndLocal;
      });

      let averageMoodType = null;
      let averageHungerLevel = null;

      if (weekRecords.length > 0) {
        const moodAvg =
          weekRecords.reduce((sum, r) => sum + r.moodTypeScore, 0) /
          weekRecords.length;
        const hungerAvg =
          weekRecords.reduce((sum, r) => sum + r.hungerLevelScore, 0) /
          weekRecords.length;

        averageMoodType = moodScoreToLabel[Math.round(moodAvg)] ?? null;
        averageHungerLevel = hungerScoreToLabel[Math.round(hungerAvg)] ?? null;
      }

      const periodLabel = `week_${i + 1} ${format(weekStartLocal, 'LLLL')}`;
      const yearLabel = format(weekStartLocal, 'yyyy');

      responseRecords.push(
        AverageMoodDTO.transform({
          year: yearLabel,
          period: periodLabel,
          averageMoodType,
          averageHungerLevel,
          startDate: format(weekStartLocal, 'yyyy-MM-dd'),
          endDate: format(weekEndLocal, 'yyyy-MM-dd'),
        }),
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Monthly Mood Averages Fetched Successfully',
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async getHalfYearlyMoodAverages(userId: string) {
    const userNow = await this.getUserTimezoneAdjustedNow(userId);

    const { moodScoreToLabel, hungerScoreToLabel } =
      this.getScoreLabelMappings();
    const recordsMap = new Map<number, any>();

    // Get current local date for the user
    const localNow = new Date(userNow);

    // Generate keys for the last 6 months including current month
    for (let i = 5; i >= 0; i--) {
      const localDate = new Date(localNow);
      localDate.setMonth(localDate.getMonth() - i, 1); // First day of the month

      const year = localDate.getFullYear();
      const month = localDate.getMonth() + 1;

      const monthKey = year * 100 + month;

      const monthLabel = localDate.toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      });
      const [monthName, yearStr] = monthLabel.split(' ');

      recordsMap.set(monthKey, {
        year: yearStr,
        period: monthName,
        averageMoodType: null,
        averageHungerLevel: null,
      });
    }

    const startMonthKey = [...recordsMap.keys()][0];

    // Fetch records within those months
    const allRecords = await this.monthlyMoodAverageModel
      .find({
        userId,
        $expr: {
          $gte: [
            { $add: [{ $multiply: ['$year', 100] }, '$month'] },
            startMonthKey,
          ],
        },
      })
      .lean();

    // Populate map with actual scores
    allRecords.forEach((record) => {
      const { month, year, moodTypeScore, hungerLevelScore } = record;
      const key = year * 100 + month;

      if (recordsMap.has(key)) {
        const dateObj = new Date(`${year}-${month}-01`);
        recordsMap.set(key, {
          year: year.toString(),
          period: dateObj.toLocaleString('default', {
            month: 'long',
          }),
          averageMoodType: moodScoreToLabel[moodTypeScore] || 'unknown',
          averageHungerLevel: hungerScoreToLabel[hungerLevelScore] || 'unknown',
        });
      }
    });

    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      AverageMoodDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Half-Yearly Mood Averages Fetched Successfully',
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async getYearlyMoodAverages(userId: string) {
    const userNow = await this.getUserTimezoneAdjustedNow(userId);

    const recordsMap = new Map<number, any>();
    const { moodScoreToLabel, hungerScoreToLabel } =
      this.getScoreLabelMappings();

    // Generate last 12 months including current (based on user-local time)
    for (let i = 11; i >= 0; i--) {
      const localDate = new Date(userNow);
      localDate.setMonth(localDate.getMonth() - i, 1); // Move back i months

      const year = localDate.getFullYear();
      const month = localDate.getMonth() + 1;

      const monthKey = year * 100 + month;

      const monthLabel = localDate.toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      });
      const [monthName, yearStr] = monthLabel.split(' ');

      recordsMap.set(monthKey, {
        year: yearStr,
        period: monthName,
        averageMoodType: null,
        averageHungerLevel: null,
      });
    }

    const startMonthKey = [...recordsMap.keys()][0];
    const endMonthKey = [...recordsMap.keys()][11];

    const allRecords = await this.monthlyMoodAverageModel
      .find({
        userId,
        $expr: {
          $and: [
            {
              $gte: [
                { $add: [{ $multiply: ['$year', 100] }, '$month'] },
                startMonthKey,
              ],
            },
            {
              $lte: [
                { $add: [{ $multiply: ['$year', 100] }, '$month'] },
                endMonthKey,
              ],
            },
          ],
        },
      })
      .lean();

    allRecords.forEach((record) => {
      const { month, year, moodTypeScore, hungerLevelScore } = record;
      const key = year * 100 + month;

      if (recordsMap.has(key)) {
        const dateObj = new Date(`${year}-${month}-01`);
        recordsMap.set(key, {
          year: year.toString(),
          period: dateObj.toLocaleString('default', {
            month: 'long',
          }),
          averageMoodType: moodScoreToLabel[moodTypeScore] || 'unknown',
          averageHungerLevel: hungerScoreToLabel[hungerLevelScore] || 'unknown',
        });
      }
    });

    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      AverageMoodDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Yearly Mood Averages Fetched Successfully',
      total: responseRecords.length,
      data: responseRecords,
    };
  }
}
