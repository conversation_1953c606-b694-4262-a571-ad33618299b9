import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserMoodRecordDTO } from './UserMoodRecord.dto';
import { IsOptional } from 'class-validator';

export class CreateUserMoodRecordsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message providing additional information about the response',
    example: 'Record created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'The user mood record details',
    type: () => UserMoodRecordDTO,
    required: false,
  })
  @IsOptional()
  record?: UserMoodRecordDTO;
}
