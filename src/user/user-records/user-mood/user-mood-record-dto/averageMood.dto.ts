import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class AverageMoodDTO {
  @ApiProperty({
    description: 'year of month e.g 2025',
  })
  @IsString()
  @IsNotEmpty()
  year: string;

  @ApiProperty({
    description: 'Time period (e.g., week 1 April)',
  })
  @IsString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({ description: 'Average mood type for the time period' })
  @IsNotEmpty()
  @IsString()
  averageMoodType: string;

  @ApiProperty({
    description: 'Average hunger level for the time period',
  })
  @IsNotEmpty()
  @IsString()
  averageHungerLevel: string;

  @ApiProperty({
    description: 'Start date of the week (YYYY-MM-DD)',
  })
  @IsString()
  @IsOptional()
  startDate: string;

  @ApiProperty({
    description: 'End date of the week (YYYY-MM-DD)',
  })
  @IsString()
  @IsOptional()
  endDate: string;

  static transform(object: {
    year: string;
    period: string;
    averageMoodType: string;
    averageHungerLevel: string;
    startDate?: string;
    endDate?: string;
  }): AverageMoodDTO {
    const transformedObj = new AverageMoodDTO();
    transformedObj.year = object.year;
    transformedObj.period = object.period;
    transformedObj.averageMoodType = object.averageMoodType;
    transformedObj.averageHungerLevel = object.averageHungerLevel;
    transformedObj.startDate = object.startDate;
    transformedObj.endDate = object.endDate;

    return transformedObj;
  }
}
