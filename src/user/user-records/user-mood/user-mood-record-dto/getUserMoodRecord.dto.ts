import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { UserMoodRecordDTO } from './UserMoodRecord.dto';

export class GetUserMoodRecordDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of sleep records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'List of sleep records',
    type: [UserMoodRecordDTO],
  })
  data: UserMoodRecordDTO[];
}
