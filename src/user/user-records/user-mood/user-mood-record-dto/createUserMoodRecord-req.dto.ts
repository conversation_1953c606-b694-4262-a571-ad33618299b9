import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { HUNGER_LEVELS, MOOD_TYPES } from 'models/user-records/Mood-Records';

export class CreateUserMoodRecordsReqDTO {
  @ApiProperty({
    description: 'Mood type of the user',
    enum: MOOD_TYPES,
    example: MOOD_TYPES.HAPPY,
  })
  @IsNotEmpty({ message: 'Mood type must not be empty' })
  @IsEnum(MOOD_TYPES, {
    message: `Mood type must be one of: ${Object.values(MOOD_TYPES).join(', ')}`,
  })
  moodType: MOOD_TYPES;

  @ApiProperty({
    description: 'Hunger level of the user',
    enum: HUNGER_LEVELS,
    example: HUNGER_LEVELS.MODERATE,
  })
  @IsNotEmpty({ message: 'Hunger level must not be empty' })
  @IsEnum(HUNGER_LEVELS, {
    message: `Hunger level must be one of: ${Object.values(HUNGER_LEVELS).join(', ')}`,
  })
  hungerLevel: HUNGER_LEVELS;
}
