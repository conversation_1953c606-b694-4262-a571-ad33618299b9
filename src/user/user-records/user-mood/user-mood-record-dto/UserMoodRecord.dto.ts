import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNotEmpty } from 'class-validator';

import {
  UserMoodRecords,
  HUNGER_LEVELS,
  MOOD_TYPES,
} from 'models/user-records/Mood-Records';
import { Types } from 'mongoose';

export class UserMoodRecordDTO {
  @ApiProperty({
    description: 'Id of mood record',
    example: '1',
  })
  @IsNotEmpty({ message: 'id is required' })
  id: string;

  @ApiProperty({
    description: 'ID of the user',
    example: '60b6c0f90b3e5e24d8a4867b',
  })
  @IsNotEmpty({ message: 'User ID must not be empty' })
  @IsMongoId({ message: 'User ID must be a valid MongoDB ObjectId' })
  userId: Types.ObjectId;

  @ApiProperty({
    description: 'Mood type of the user',
    enum: MOOD_TYPES,
    example: MOOD_TYPES.HAPPY,
  })
  @IsNotEmpty({ message: 'Mood type must not be empty' })
  @IsEnum(MOOD_TYPES, {
    message: `Mood type must be one of: ${Object.values(MOOD_TYPES).join(', ')}`,
  })
  moodType: MOOD_TYPES;

  @ApiProperty({
    description: 'Hunger level of the user',
    enum: HUNGER_LEVELS,
    example: HUNGER_LEVELS.MODERATE,
  })
  @IsNotEmpty({ message: 'Hunger level must not be empty' })
  @IsEnum(HUNGER_LEVELS, {
    message: `Hunger level must be one of: ${Object.values(HUNGER_LEVELS).join(', ')}`,
  })
  hungerLevel: HUNGER_LEVELS;

  createdAt: Date;
  updatedAt: Date;

  // Transform logic
  static transform(object: UserMoodRecords): UserMoodRecordDTO {
    const transformedObj = new UserMoodRecordDTO();
    transformedObj.id = object._id.toString();
    transformedObj.userId = object.userId;
    transformedObj.moodType = object.moodType as MOOD_TYPES;
    transformedObj.hungerLevel = object.hungerLevel as HUNGER_LEVELS;
    transformedObj.createdAt = object.createdAt;
    transformedObj.updatedAt = (object as any).updatedAt;

    return transformedObj;
  }
}
