import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { AverageMoodDTO } from './averageMood.dto';

export class GetUserMoodAvgRecordDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of mood records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'List of mood records',
    type: [AverageMoodDTO],
  })
  data: AverageMoodDTO[];
}
