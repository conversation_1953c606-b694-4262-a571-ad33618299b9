import { IsOptional, IsEnum } from 'class-validator';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { MOOD_TYPES, HUNGER_LEVELS } from 'models/user-records/Mood-Records';
import { BaseResponse } from 'src/utils/responses';
import { UserMoodRecordDTO } from './UserMoodRecord.dto';

export class UpdateMoodRecordReqDto {
  @ApiPropertyOptional({ enum: MOOD_TYPES, description: 'Type of mood' })
  @IsOptional()
  @IsEnum(MOOD_TYPES)
  moodType?: MOOD_TYPES;

  @ApiPropertyOptional({ enum: HUNGER_LEVELS, description: 'Level of hunger' })
  @IsOptional()
  @IsEnum(HUNGER_LEVELS)
  hungerLevel?: HUNGER_LEVELS;
}

export class UpdateMoodRecordResDto extends BaseResponse {
  @ApiProperty({ example: 'Mood record updated successfully' })
  msg: string;

  @ApiProperty({ type: UserMoodRecordDTO })
  data: UserMoodRecordDTO;
}
