import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Request } from 'express';
import { UserMealService } from './user-meals.service';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import {
  AddMealRecordReqDTO,
  AddMealRecordResDTO,
  GetMealRecordResDTO,
  GetMealRecordsAnalyticsResDTO,
  GetSingleMealRecordResDTO,
  UpdateMealRecordReqDTO,
  UpdateMealRecordResDTO,
} from './dto';
import { getMealRecordQueryFilterInterface } from './interface';
import { ErrorResponse } from 'src/utils/responses';

import { GetUserAnalyticsQueryInterface } from 'src/common/interfaces';

@ApiTags('User-Meal-Records')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class UserMealController {
  constructor(private readonly mealRecordsService: UserMealService) {}

  @ApiResponse({
    status: 200,
    description: 'User meal record retrived successfully.',
    type: GetMealRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('meal_records')
  async GetMealRecords(
    @Req() req: Request,
    @Query() queryFilter: getMealRecordQueryFilterInterface,
  ) {
    const user = req['user'];

    return this.mealRecordsService.getMealRecord(user, queryFilter);
  }

  @ApiResponse({
    status: 200,
    description: 'Single User meal record retrived successfully.',
    type: GetSingleMealRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('meal_records/:mealId')
  async GetSingleMealRecords(
    @Req() req: Request,
    @Param('mealId') mealId: string,
  ) {
    const user = req['user'];

    return this.mealRecordsService.getSingleMealRecord(user, mealId);
  }

  @ApiResponse({
    status: 200,
    description: 'User meal record addded successfully.',
    type: AddMealRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('meal_records')
  async AddMealRecord(
    @Req() req: Request,
    @Body() mealRecordData: AddMealRecordReqDTO,
  ) {
    const user = req['user'];

    return this.mealRecordsService.addMealRecord(user, mealRecordData);
  }

  @ApiResponse({
    status: 200,
    description: 'User meal record updated successfully.',
    type: UpdateMealRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('meal_records/:mealRecordId')
  async UpdateMealRecord(
    @Req() req: Request,
    @Param('mealRecordId') mealRecordId: string,
    @Body() updatedata: UpdateMealRecordReqDTO,
  ) {
    const user = req['user'];

    return this.mealRecordsService.updateMealRecord(
      user,
      mealRecordId,
      updatedata,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'User meal record analytics retrived successfully.',
    type: GetMealRecordsAnalyticsResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/meal_analytics')
  async GetMealRecordsAnalytics(
    @Req() req: Request,
    @Query() queryFilters: GetUserAnalyticsQueryInterface,
  ) {
    const user = req['user'];

    return this.mealRecordsService.getMealRecordAnalytics(user, queryFilters);
  }
}
