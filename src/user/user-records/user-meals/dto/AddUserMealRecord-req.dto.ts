import { MealEntryDTO } from './meal-entry.dto';
import {
  IsString,
  IsDate,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  ArrayMinSize,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { MEAL_NAMES } from 'models/user-records/Meal-Records';

export class AddMealRecordReqDTO {
  @ApiProperty({ description: 'Name of the meal', example: 'Breakfast' })
  @IsEnum(MEAL_NAMES)
  @IsString()
  @IsNotEmpty()
  mealName: string;

  @ApiProperty({
    description: 'Meal time details',
    example: '8:00-10:00',
  })
  @IsString()
  @IsNotEmpty()
  mealTime: string;

  @ApiProperty({
    description: 'Date of the meal record',
    example: '2023-01-01',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  date: Date;

  @ApiProperty({ type: [MealEntryDTO], description: 'List of meal entries' })
  @ValidateNested({ each: true })
  @Type(() => MealEntryDTO)
  @ArrayMinSize(1)
  @IsArray()
  @IsNotEmpty()
  meals: MealEntryDTO[];
}
