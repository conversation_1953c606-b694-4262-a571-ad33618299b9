import { MealmonthlyAverageRecords } from 'models/user-records/Meal-Records';

export class MonthlyMealRecordDTO {
  id: string;
  month: number;
  year: number;
  protein: number;
  calories: number;
  fats: number;
  fiber: number;
  carbs: number;

  static transform(
    object: MealmonthlyAverageRecords,
    showFullData: boolean = false,
  ): MonthlyMealRecordDTO {
    const tranformedobj = new MonthlyMealRecordDTO();

    tranformedobj.id = object._id.toString();

    if (showFullData) {
      tranformedobj.month = object.month;
      tranformedobj.year = object.year;
    }

    tranformedobj.protein = object.protein;
    tranformedobj.calories = object.calories;
    tranformedobj.fats = object.fats;
    tranformedobj.fiber = object.fiber;
    tranformedobj.carbs = object.carbs;

    return tranformedobj;
  }
}
