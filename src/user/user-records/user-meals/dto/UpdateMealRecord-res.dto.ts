import { BaseResponse } from 'src/utils/responses';
import { MealRecordDTO } from './meal-record.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateMealRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Meal record updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated meal record data',
    type: MealRecordDTO,
  })
  data: MealRecordDTO;
}
