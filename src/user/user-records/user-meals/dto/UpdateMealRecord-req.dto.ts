import { MealEntryDTO } from './meal-entry.dto';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { MEAL_NAMES } from 'models/user-records/Meal-Records';

export class UpdateMealRecordReqDTO {
  @ApiProperty({
    description: 'Name of the meal to update',
    example: 'Lunch',
  })
  @IsEnum(MEAL_NAMES)
  @IsString()
  @IsOptional()
  mealName: string;

  @ApiProperty({
    description: 'Meal time details',
    example: '8:00-10:00',
  })
  @IsString()
  mealTime: string;

  @ApiProperty({
    description: 'Updated array of meal entries',
    type: [MealEntryDTO],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MealEntryDTO)
  @IsOptional()
  meals: MealEntryDTO[];
}
