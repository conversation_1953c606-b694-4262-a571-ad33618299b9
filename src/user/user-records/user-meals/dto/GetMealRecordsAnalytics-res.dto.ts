import { BaseResponse } from 'src/utils/responses';
import { MonthlyMealRecordDTO } from './monthly-meal-record.dto';
import { DailyMealRecordDTO } from './daily-meal-record.dto';
import { nutritionSumInterface } from '../interface';

export class GetMealRecordsAnalyticsResDTO extends BaseResponse {
  total: number;
  data: AnalyticsDataDTO[];
}

class AnalyticsDataDTO {
  day?: string;

  weekNumber?: number;
  period?: string;

  year?: number;
  month?: number;
  monthName?: string;

  data: DailyMealRecordDTO | MonthlyMealRecordDTO | nutritionSumInterface;
}
