import { UserMealRecords } from 'models/user-records/Meal-Records';
import { MealEntryDTO } from './meal-entry.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsISO8601, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class MealRecordDTO {
  @ApiProperty({
    description: 'Record ID',
    example: '123456789',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: '64c9e4f5e4b0a1b2c3d4e5f6',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Name of the meal',
    example: 'Breakfast',
  })
  @IsString()
  mealName: string;

  @ApiProperty({
    description: 'Date of the meal in ISO format',
    example: '2023-08-01T12:00:00.000Z',
  })
  @IsISO8601()
  date: string;

  @ApiProperty({
    description: 'Meal time details',
    example: '8:00-10:00',
  })
  @IsString()
  mealTime: string;

  @ApiProperty({
    description: 'Array of meal entries',
    type: [MealEntryDTO],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MealEntryDTO)
  meals: MealEntryDTO[];

  createdAt: Date;
  updatedAt: Date;

  static transform(object: UserMealRecords): MealRecordDTO {
    const tranformedobj = new MealRecordDTO();
    tranformedobj.id = object._id.toString();
    tranformedobj.mealName = object.mealName;
    tranformedobj.date = object.date.toISOString();
    tranformedobj.mealTime = object.mealTime;
    tranformedobj.createdAt = object.createdAt;
    tranformedobj.updatedAt = (object as any).updatedAt;

    if (object.meals) {
      tranformedobj.meals = object.meals.map((item) =>
        MealEntryDTO.transform(item),
      );
    }
    return tranformedobj;
  }
}
