import { BaseResponse } from 'src/utils/responses';
import { MealRecordDTO } from './meal-record.dto';
import { ApiProperty } from '@nestjs/swagger';

export class GetMealRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of meal records',
    example: 15,
    type: Number,
  })
  total: number;

  @ApiProperty({
    description: 'Array of meal records',
    type: [MealRecordDTO],
  })
  data: MealRecordDTO[];
}
