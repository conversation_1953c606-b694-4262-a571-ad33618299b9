import { BaseResponse } from 'src/utils/responses';
import { MealRecordDTO } from './meal-record.dto';
import { ApiProperty } from '@nestjs/swagger';

export class AddMealRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Meal record added successfully',
  })
  msg: string;

  @ApiProperty({
    type: MealRecordDTO,
    description: 'The added meal record data',
  })
  data: MealRecordDTO;
}
