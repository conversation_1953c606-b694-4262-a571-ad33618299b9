import { MealDailyAverageRecords } from 'models/user-records/Meal-Records';

export class DailyMealRecordDTO {
  id: string;
  month: number;
  year: number;
  date: Date;
  protein: number;
  calories: number;
  fats: number;
  fiber: number;
  carbs: number;

  static transform(
    object: MealDailyAverageRecords,
    showFullData: boolean = false,
  ): DailyMealRecordDTO {
    const tranformedobj = new DailyMealRecordDTO();

    tranformedobj.id = object._id.toString();

    if (showFullData) {
      tranformedobj.month = object.month;
      tranformedobj.year = object.year;
      tranformedobj.date = object.date;
    }

    tranformedobj.protein = object.protein;
    tranformedobj.calories = object.calories;
    tranformedobj.fats = object.fats;
    tranformedobj.fiber = object.fiber;
    tranformedobj.carbs = object.carbs;

    return tranformedobj;
  }
}
