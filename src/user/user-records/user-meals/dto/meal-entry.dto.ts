import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { NUTRITION_QUANTITY } from 'models/recipe';
import {
  MealEntry,
  NUTRITION_MEASUREMENT,
} from 'models/user-records/Meal-Records';

export class MealEntryDTO {
  @ApiProperty({
    description: 'ID of the recipe',
    example: '64c9e4f5e4b0a1b2c3d4e5f6',
  })
  @IsString()
  @IsNotEmpty()
  recipeId: string;

  @ApiProperty({
    description: 'Quantity of the meal',
    enum: NUTRITION_QUANTITY,
  })
  @IsEnum(NUTRITION_QUANTITY)
  @IsNotEmpty()
  quantity: NUTRITION_QUANTITY;

  @ApiProperty({
    description: 'Measurement unit',
    enum: NUTRITION_MEASUREMENT,
  })
  @IsEnum(NUTRITION_MEASUREMENT)
  @IsNotEmpty()
  measurement: NUTRITION_MEASUREMENT;

  @ApiProperty({
    description: 'Protein content (optional)',
    example: 25,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  protein?: number;

  @ApiProperty({
    description: 'Calories content (optional)',
    example: 300,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  calories?: number;

  @ApiProperty({
    description: 'Fats content (optional)',
    example: 10,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  fats?: number;

  @ApiProperty({
    description: 'Fiber content (optional)',
    example: 5,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  fiber?: number;

  @ApiProperty({
    description: 'Carbohydrates content (optional)',
    example: 40,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  carbs?: number;

  @ApiProperty({
    description: 'Recipe Name content (optional)',
    example: 'toast',
    required: false,
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    description: 'Recipe thumbnail Url (optional)',
    example: 'url',
    required: false,
  })
  @IsString()
  @IsOptional()
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Author of the recipe (userId or null for admin)',
    required: false,
  })
  @IsString()
  @IsOptional()
  author?: string;

  static transform(object: MealEntry): MealEntryDTO {
    const tranformedobj = new MealEntryDTO();
    tranformedobj.recipeId = object.recipeId.toString();
    tranformedobj.quantity = object.quantity as NUTRITION_QUANTITY;
    tranformedobj.measurement = object.measurement as NUTRITION_MEASUREMENT;

    tranformedobj.title = object.title;
    tranformedobj.thumbnailUrl = object.thumbnailUrl;

    tranformedobj.protein = object.protein;
    tranformedobj.calories = object.calories;
    tranformedobj.fats = object.fats;
    tranformedobj.fiber = object.fiber;
    tranformedobj.carbs = object.carbs;

    tranformedobj.author = (object as any).author ?? null;

    return tranformedobj;
  }
}
