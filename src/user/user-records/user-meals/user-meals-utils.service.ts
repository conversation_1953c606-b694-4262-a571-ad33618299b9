import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { User } from 'models/user';
import * as moment from 'moment';
import { ClientSession, Model, Types } from 'mongoose';
import {
  AddMealRecordReqDTO,
  DailyMealTotalRecordDTO,
  GetMealRecordsAnalyticsResDTO,
  MealEntryDTO,
  MonthlyMealRecordDTO,
  UpdateMealRecordReqDTO,
} from './dto';
import { InjectModel } from '@nestjs/mongoose';
import {
  MealDailyAverageRecords,
  MealDailyTotalRecord,
  MealEntry,
  MealmonthlyAverageRecords,
  UserMealRecords,
} from 'models/user-records/Meal-Records';
import { Recipes } from 'models/recipe';
import { nutritionSumInterface } from './interface';

@Injectable()
export class UserMealRecordsUtilsService {
  constructor(
    @InjectModel(UserMealRecords.name)
    private mealRecordModel: Model<UserMealRecords>,

    @InjectModel(MealDailyAverageRecords.name)
    private mealDailyRecordModel: Model<MealDailyAverageRecords>,

    @InjectModel(MealDailyTotalRecord.name)
    private mealDailyTotalRecordModel: Model<MealDailyTotalRecord>,

    @InjectModel(MealmonthlyAverageRecords.name)
    private mealMonthlyRecordModel: Model<MealmonthlyAverageRecords>,

    @InjectModel(Recipes.name)
    private recipeModel: Model<Recipes>,
  ) {}

  async processAndValidateMeals(
    user: User,
    meals: any[],
    session: ClientSession,
  ): Promise<MealEntryDTO[]> {
    // Validate meal IDs
    this.validateMealIds(meals);

    // Fetch recipes and validate ownership
    const uniqueRecipes = await this.fetchUserAccessibleRecipes(
      user,
      meals,
      session,
    );

    // Create a map for quick lookup
    const recipeMap = new Map(
      uniqueRecipes.map((recipe) => [recipe._id.toString(), recipe]),
    );

    // Process meals with nutrition data
    return this.processMealsWithNutrition(meals, recipeMap);
  }

  validateMealIds(meals: any[]): void {
    const invalidIds = meals
      .filter(
        (item) =>
          !item.recipeId.includes('custom') &&
          !Types.ObjectId.isValid(item.recipeId),
      )
      .map((item) => item.recipeId);

    if (invalidIds.length > 0) {
      throw new BadRequestException(
        `Invalid Recipe ID(s) provided: ${invalidIds.join(', ')}`,
      );
    }
  }

  async fetchUserAccessibleRecipes(
    user: User,
    meals: any[],
    session: ClientSession,
  ): Promise<any[]> {
    return this.recipeModel
      .find({
        _id: { $in: meals.map((item) => item.recipeId) },
        $or: [{ author: user._id }, { author: null }],
      })
      .session(session);
  }

  processMealsWithNutrition(
    meals: any[],
    recipeMap: Map<string, any>,
  ): MealEntryDTO[] {
    //
    return meals.map((meal) => {
      const recipe: Recipes = recipeMap.get(meal.recipeId);

      if (!recipe) {
        throw new BadRequestException(
          `Recipe with ID ${meal.recipeId} not found!`,
        );
      }

      const nutritions = recipe.nutritionByQuantity.find(
        (item) => item.quantity === meal.quantity,
      );

      if (!nutritions) {
        throw new InternalServerErrorException('Nutrition not found !!');
      }

      return {
        recipeId: meal.recipeId,
        measurement: meal.measurement,

        title: recipe.title,
        thumbnailUrl: recipe.thumbnailUrl,

        quantity: meal.quantity,
        calories: nutritions.calories,
        carbs: nutritions.carbs,
        fats: nutritions.fats,
        fiber: nutritions.fiber,
        protein: nutritions.protein,
      };
    });
  }

  async createMealRecord(
    user: User,
    mealRecordData: AddMealRecordReqDTO,
    validMealsArr: MealEntryDTO[],
    session: ClientSession,
  ): Promise<any> {
    const date = new Date(mealRecordData.date);

    console.log({ date });

    // Check for existing meal record for the same date and mealName
    const existingMeal = await this.mealRecordModel
      .findOne({
        userId: user._id,
        date,
        mealName: mealRecordData.mealName,
        isDeleted: false, // if you're using soft deletion
      })
      .session(session);

    if (existingMeal) {
      const formattedDate = moment(date).format('DD-MM-YYYY');

      throw new BadRequestException(
        `Meal record for ${mealRecordData.mealName} already exists on ${formattedDate}`,
      );
    }

    return this.mealRecordModel.create(
      [
        {
          userId: user._id,
          mealName: mealRecordData.mealName,
          date,
          mealTime: mealRecordData.mealTime,
          meals: validMealsArr,
        },
      ],
      { session },
    );
  }

  async updateDailyRecord(
    user: User,
    date: Date,
    month: number,
    year: number,
    session: ClientSession,
  ): Promise<void> {
    // Find or create daily record
    const existingDailyRecord = await this.findOrCreateDailyRecord(
      user,
      date,
      month,
      year,
      session,
    );

    // Calculate daily averages
    const dailyMealRecords = await this.mealRecordModel
      .find({
        date,
        userId: user._id,
        isDeleted: false,
      })
      .session(session);

    const dailyAverages = this.calculateNutritionAverages(
      dailyMealRecords.map((record) => record.meals).flat(),
      dailyMealRecords.map((record) => record.meals).flat().length,
    );

    // Update daily record
    await this.mealDailyRecordModel
      .findByIdAndUpdate(
        existingDailyRecord._id,
        { ...dailyAverages },
        { new: true },
      )
      .session(session);
  }

  async findOrCreateDailyRecord(
    user: User,
    date: Date,
    month: number,
    year: number,
    session: ClientSession,
  ): Promise<any> {
    let existingRecord = await this.mealDailyRecordModel
      .findOne({
        date,
        userId: user._id,
      })
      .session(session);

    if (!existingRecord) {
      const newRecord = await this.mealDailyRecordModel.create(
        [
          {
            userId: user._id,
            month,
            year,
            date,
          },
        ],
        { session },
      );
      existingRecord = newRecord[0];
    }

    return existingRecord;
  }

  async updateMonthlyRecord(
    user: User,
    month: number,
    year: number,
    session: ClientSession,
  ): Promise<void> {
    // Find or create monthly record
    const existingMonthlyRecord = await this.findOrCreateMonthlyRecord(
      user,
      month,
      year,
      session,
    );

    // Calculate monthly averages
    const monthlyMealRecords = await this.mealDailyRecordModel
      .find({
        userId: user._id,
        month,
        year,
      })
      .session(session);

    const monthlyAverages = this.calculateNutritionAverages(
      monthlyMealRecords,
      monthlyMealRecords.length,
    );

    // Update monthly record
    await this.mealMonthlyRecordModel
      .findByIdAndUpdate(
        existingMonthlyRecord._id,
        { ...monthlyAverages },
        { new: true },
      )
      .session(session);
  }

  async findOrCreateMonthlyRecord(
    user: User,
    month: number,
    year: number,
    session: ClientSession,
  ): Promise<any> {
    let existingRecord = await this.mealMonthlyRecordModel
      .findOne({
        userId: user._id,
        month,
        year,
      })
      .session(session);

    if (!existingRecord) {
      const newRecord = await this.mealMonthlyRecordModel.create(
        [
          {
            userId: user._id,
            month,
            year,
          },
        ],
        { session },
      );
      existingRecord = newRecord[0];
    }

    return existingRecord;
  }

  safeDivide(num: number, denom: number) {
    const ans = denom === 0 ? 0 : Math.round(num / denom);

    return ans;
  }

  calculateNutritionAverages(
    items: MealEntry[] | MealDailyAverageRecords[],
    count: number,
  ): nutritionSumInterface {
    const sum: nutritionSumInterface = {
      protein: 0,
      calories: 0,
      fats: 0,
      fiber: 0,
      carbs: 0,
    };

    items.forEach((item) => {
      sum.calories += item.calories;
      sum.protein += item.protein;
      sum.fats += item.fats;
      sum.fiber += item.fiber;
      sum.carbs += item.carbs;
    });

    const averageData: nutritionSumInterface = {
      protein: this.safeDivide(sum.protein, count),
      calories: this.safeDivide(sum.calories, count),
      fats: this.safeDivide(sum.fats, count),
      fiber: this.safeDivide(sum.fiber, count),
      carbs: this.safeDivide(sum.carbs, count),
    };

    return averageData;
  }

  calculateNutritionTotals(
    items: MealEntry[] | MealDailyTotalRecord[],
  ): nutritionSumInterface {
    const total: nutritionSumInterface = {
      protein: 0,
      calories: 0,
      fats: 0,
      fiber: 0,
      carbs: 0,
    };

    items.forEach((item) => {
      total.calories += item.calories;
      total.protein += item.protein;
      total.fats += item.fats;
      total.fiber += item.fiber;
      total.carbs += item.carbs;
    });

    return total;
  }

  async updateDailyTotalRecord(
    user: User,
    date: Date,
    month: number,
    year: number,
    session: ClientSession,
  ): Promise<void> {
    // Normalize date to start of the day in UTC
    const normalizedDate = moment(date).utc().startOf('day').toDate();

    // Find or create daily total record
    const existingTotalRecord =
      await this.mealDailyTotalRecordModel.findOneAndUpdate(
        {
          userId: user._id,
          date: normalizedDate,
          month,
          year,
        },
        {},
        { upsert: true, new: true, setDefaultsOnInsert: true, session },
      );

    // Get all meal records for the same normalized day
    const dailyMealRecords = await this.mealRecordModel
      .find({
        userId: user._id,
        date: {
          $gte: normalizedDate,
          $lt: moment(normalizedDate).add(1, 'day').toDate(),
        },
        isDeleted: false,
      })
      .session(session);

    const allMeals = dailyMealRecords.map((record) => record.meals).flat();

    const nutritionTotals = this.calculateNutritionTotals(allMeals);

    // Update total record
    await this.mealDailyTotalRecordModel
      .findByIdAndUpdate(
        existingTotalRecord._id,
        { ...nutritionTotals },
        { new: true },
      )
      .session(session);
  }

  prepareUpdateData(updateMealRecordData: UpdateMealRecordReqDTO): any {
    const updateData: any = {};

    if (updateMealRecordData.mealName) {
      updateData.mealName = updateMealRecordData.mealName;
    }

    if (updateMealRecordData.mealTime) {
      updateData.mealTime = updateMealRecordData.mealTime;
    }

    return updateData;
  }

  //----Analytics-----

  async getWeeklyMealRecords(
    user: User,
  ): Promise<GetMealRecordsAnalyticsResDTO> {
    try {
      const timezone = user.timeZone || 'UTC'; // fallback if timezone not set

      // Get today's end (23:59:59.999) in user's timezone
      const today = moment.tz(timezone).endOf('day');

      // 6 days ago from today (start of that day)
      const sevenDaysAgo = today.clone().subtract(6, 'days').startOf('day');

      // Convert to UTC for MongoDB query
      const utcStart = sevenDaysAgo.clone().tz('UTC').toDate();
      const utcEnd = today.clone().tz('UTC').toDate();

      // Use total record model instead of average
      const weeklyRecords = await this.mealDailyTotalRecordModel
        .find({
          userId: user._id,
          date: { $gte: utcStart, $lte: utcEnd },
        })
        .sort({ date: 1 });

      const formattedRecords = [];

      for (let i = 0; i < 7; i++) {
        const currentLocalDate = sevenDaysAgo.clone().add(i, 'days');

        const dayStartUTC = currentLocalDate
          .clone()
          .startOf('day')
          .tz('UTC')
          .toDate();
        const dayEndUTC = currentLocalDate
          .clone()
          .endOf('day')
          .tz('UTC')
          .toDate();

        const matchingRecord = weeklyRecords.find((record) => {
          const recordDate = moment(record.date);
          return recordDate.isBetween(dayStartUTC, dayEndUTC, undefined, '[]');
        });

        formattedRecords.push({
          year: currentLocalDate.year(),
          period: currentLocalDate.format('YYYY-MM-DD'),
          data: matchingRecord
            ? DailyMealTotalRecordDTO.transform(matchingRecord)
            : null,
        });
      }

      return {
        error: false,
        statusCode: 200,
        total: formattedRecords.length,
        data: formattedRecords,
      };
    } catch (error) {
      throw error;
    }
  }

  async getHalfYearlyMealRecords(
    user: User,
    range: number,
  ): Promise<GetMealRecordsAnalyticsResDTO> {
    try {
      const userTimeZone = user.timeZone || 'UTC';
      const today = moment().tz(userTimeZone).startOf('month');

      const targetMonths = [];

      // Collect months from (current - range + 1) up to current month (inclusive)
      for (let i = range - 1; i >= 0; i--) {
        const monthMoment = today.clone().subtract(i, 'months');
        targetMonths.push({
          year: monthMoment.year(),
          month: monthMoment.month() + 1, // JavaScript months are 0-indexed
        });
      }

      const monthConditions = targetMonths.map((item) => ({
        year: item.year,
        month: item.month,
      }));

      const mealRecords = await this.mealMonthlyRecordModel
        .find({
          userId: user._id,
          $or: monthConditions,
        })
        .lean();

      const result = targetMonths.map((target) => {
        const foundRecord = mealRecords.find(
          (rec) => rec.year === target.year && rec.month === target.month,
        );

        const period = moment()
          .month(target.month - 1)
          .year(target.year)
          .tz(userTimeZone)
          .format('MMMM');

        return {
          year: target.year,
          month: target.month,
          period,
          data: foundRecord
            ? MonthlyMealRecordDTO.transform(foundRecord)
            : null,
        };
      });

      return {
        error: false,
        statusCode: 200,
        total: result.length,
        data: result,
      };
    } catch (error) {
      throw error;
    }
  }

  async getMonthlyMealRecords(
    user: User,
  ): Promise<GetMealRecordsAnalyticsResDTO> {
    try {
      const userTimeZone = user.timeZone || 'UTC';

      // Get the current time in user's timezone and truncate to end of local day
      const today = new Date(
        new Date().toLocaleString('en-US', { timeZone: userTimeZone }),
      );
      today.setHours(23, 59, 59, 999);

      // Get the start date for 30 days ago (start of that day)
      const thirtyDaysAgo = new Date(today);
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 29); // include today
      thirtyDaysAgo.setHours(0, 0, 0, 0);

      // Fetch meal records within the last 30 days (excluding future)
      const monthlyRecords = await this.mealDailyRecordModel
        .find({
          userId: user._id,
          date: { $gte: thirtyDaysAgo, $lte: today },
        })
        .sort({ date: 1 })
        .lean();

      // Divide the 30-day period into 5 weeks (6 days each except possibly the last one)
      const weeks = [];
      for (let i = 0; i < 5; i++) {
        const start = new Date(thirtyDaysAgo);
        start.setDate(start.getDate() + i * 7);

        const end = new Date(start);
        end.setDate(end.getDate() + 6);

        // Clamp end date to today (no future)
        if (end > today) {
          end.setTime(today.getTime());
        }

        weeks.push({
          period: `week_${i + 1}`,
          startDate: new Date(start),
          endDate: new Date(end),
          records: [],
        });
      }

      // Distribute records into week buckets based on their date
      monthlyRecords.forEach((record: MealDailyAverageRecords) => {
        const recordDate = new Date(record.date);
        for (const week of weeks) {
          if (recordDate >= week.startDate && recordDate <= week.endDate) {
            week.records.push(record);
            break;
          }
        }
      });

      // Format each week's data
      const weeklyAverages = weeks.map((week) => {
        const startDateFormatted = week.startDate.toISOString().split('T')[0];
        const endDateFormatted = week.endDate.toISOString().split('T')[0];

        return {
          year: week.startDate.getFullYear(),
          period: week.period,
          data:
            week.records.length > 0
              ? this.calculateWeeklyAverages(week.records)
              : null,
          startDate: startDateFormatted,
          endDate: endDateFormatted,
        };
      });

      return {
        error: false,
        statusCode: 200,
        total: weeklyAverages.length,
        data: weeklyAverages,
      };
    } catch (error) {
      throw error;
    }
  }

  private calculateWeeklyAverages(
    weekData: MealDailyAverageRecords[],
  ): nutritionSumInterface | null {
    if (!weekData.length) {
      return null;
    }

    // Initialize sum variables
    let totalCalories = 0;
    let totalProtein = 0;
    let totalCarbs = 0;
    let totalFat = 0;
    let totalFiber = 0;

    // Sum up all values
    weekData.forEach((record) => {
      totalCalories += record.calories || 0;
      totalProtein += record.protein || 0;
      totalCarbs += record.carbs || 0;
      totalFat += record.fats || 0;
      totalFiber += record.fiber || 0;
    });

    // Calculate averages
    return {
      calories: weekData.length
        ? +(totalCalories / weekData.length).toFixed(2)
        : null,
      protein: weekData.length
        ? +(totalProtein / weekData.length).toFixed(2)
        : null,
      carbs: weekData.length
        ? +(totalCarbs / weekData.length).toFixed(2)
        : null,
      fats: weekData.length ? +(totalFat / weekData.length).toFixed(2) : null,
      fiber: weekData.length
        ? +(totalFiber / weekData.length).toFixed(2)
        : null,
    };
  }
}
