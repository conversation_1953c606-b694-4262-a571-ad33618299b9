import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Connection, Model, Types } from 'mongoose';
import { UserMealRecords } from 'models/user-records/Meal-Records';
import {
  AddMealRecordReqDTO,
  AddMealRecordResDTO,
  DeleteMealRecordResDTO,
  GetMealRecordResDTO,
  GetMealRecordsAnalyticsResDTO,
  GetSingleMealRecordResDTO,
  MealRecordDTO,
  UpdateMealRecordReqDTO,
  UpdateMealRecordResDTO,
} from './dto';
import { getMealRecordQueryFilterInterface } from './interface';
import * as moment from 'moment';
import { CustomLogger } from 'src/common/services';
import { UserMealRecordsUtilsService } from './user-meals-utils.service';
import {
  AnalyticsRecordFilter,
  GetUserAnalyticsQueryInterface,
} from 'src/common/interfaces';
import { Recipes } from 'models/recipe';

@Injectable()
export class UserMealService {
  constructor(
    @InjectModel(UserMealRecords.name)
    private mealRecordModel: Model<UserMealRecords>,

    @InjectModel(Recipes.name)
    private recipeModel: Model<Recipes>,

    @InjectConnection() private readonly connection: Connection,

    private readonly logger: CustomLogger,
    private readonly userMealUtilsService: UserMealRecordsUtilsService,
  ) {}

  async getMealRecord(
    user: User,
    queryFilter: getMealRecordQueryFilterInterface,
  ): Promise<GetMealRecordResDTO> {
    const { date } = queryFilter;

    if (!date) {
      throw new BadRequestException('Please provide a valid date !!');
    }

    const userTimeZone = user.timeZone || 'UTC';

    // Parse and validate the input date in user's timezone
    const userDate = moment.tz(date, 'YYYY-MM-DD', true, userTimeZone); // strict parsing
    if (!userDate.isValid()) {
      throw new BadRequestException('Invalid Date provided !!');
    }

    // Get UTC start and end of the day based on user's timezone
    const startOfDayUTC = userDate.clone().startOf('day').utc().toDate();
    const endOfDayUTC = userDate.clone().endOf('day').utc().toDate();

    console.log('Start of Day (UTC):', startOfDayUTC);
    console.log('End of Day (UTC):', endOfDayUTC);

    const query: any = {
      userId: user._id,
      isDeleted: false,
      date: {
        $gte: startOfDayUTC,
        $lte: endOfDayUTC,
      },
    };

    const mealRecords = await this.mealRecordModel
      .find(query)
      .sort({ createdAt: 1 })
      .lean();

    if (!mealRecords || mealRecords.length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        total: 0,
        data: [],
      };
    }

    const resp = mealRecords.map((item) => MealRecordDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: resp.length,
      data: resp,
    };
  }

  async getSingleMealRecord(
    user: User,
    mealId: string,
  ): Promise<GetSingleMealRecordResDTO> {
    //
    if (!Types.ObjectId.isValid(mealId)) {
      throw new BadRequestException('Invalid id provided !!');
    }

    const mealRecord = await this.mealRecordModel.findById(mealId).lean();

    if (!mealRecord || mealRecord.isDeleted || mealRecord.userId !== user._id) {
      throw new BadRequestException('Meal record not found !!');
    }

    // For each meal, fetch the recipe and get the author
    const mealsWithAuthor = await Promise.all(
      mealRecord.meals.map(async (meal) => {
        if (meal.recipeId) {
          const recipe = await this.recipeModel.findById(meal.recipeId).lean();
          return {
            ...meal,
            author: recipe && recipe.author ? recipe.author : null,
          };
        }
        return meal;
      }),
    );

    const resp = MealRecordDTO.transform({
      ...mealRecord,
      meals: mealsWithAuthor,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }

  async addMealRecord(
    user: User,
    mealRecordData: AddMealRecordReqDTO,
  ): Promise<AddMealRecordResDTO> {
    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      // Validate input data
      //   this.userMealUtilsService.validateDateFormat(mealRecordData.date);

      const dateObj = moment(mealRecordData.date, 'YYYY-MM-DD');
      const month = dateObj.month() + 1;
      const year = dateObj.year();

      // Process and validate meals
      const validMealsArr =
        await this.userMealUtilsService.processAndValidateMeals(
          user,
          mealRecordData.meals,
          session,
        );

      // Create meal record
      const newMealRecord = await this.userMealUtilsService.createMealRecord(
        user,
        mealRecordData,
        validMealsArr,
        session,
      );

      // Update daily avearge record
      await this.userMealUtilsService.updateDailyRecord(
        user,
        mealRecordData.date,
        month,
        year,
        session,
      );

      // Update daily total record
      await this.userMealUtilsService.updateDailyTotalRecord(
        user,
        mealRecordData.date,
        month,
        year,
        session,
      );

      // Update monthly record
      await this.userMealUtilsService.updateMonthlyRecord(
        user,
        month,
        year,
        session,
      );

      await session.commitTransaction();

      const resp = MealRecordDTO.transform(newMealRecord[0]);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Meal record added successfully!',
        data: resp,
      };
    } catch (error) {
      await session.abortTransaction();
      this.logger.error(error);

      throw error;
    } finally {
      session.endSession();
    }
  }

  async updateMealRecord(
    user: User,
    mealRecordId: string,
    updateMealRecordData: UpdateMealRecordReqDTO,
  ): Promise<UpdateMealRecordResDTO> {
    if (!Types.ObjectId.isValid(mealRecordId)) {
      throw new BadRequestException('Invalid record id provided !!');
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      // Find existing record and validate
      const existingRecord = await this.mealRecordModel
        .findById(mealRecordId)
        .session(session);

      if (
        !existingRecord ||
        existingRecord.isDeleted ||
        existingRecord.userId.toString() !== user._id.toString()
      ) {
        throw new BadRequestException('Meal record not found !!');
      }

      // Prepare update data
      const updateData =
        this.userMealUtilsService.prepareUpdateData(updateMealRecordData);

      // Process meals if provided
      if (
        updateMealRecordData.meals &&
        Array.isArray(updateMealRecordData.meals)
      ) {
        updateData.meals =
          await this.userMealUtilsService.processAndValidateMeals(
            user,
            updateMealRecordData.meals,
            session,
          );
      }

      // Update meal record
      const updatedMealRecord = await this.mealRecordModel.findByIdAndUpdate(
        mealRecordId,
        updateData,
        { runValidators: true, new: true, session },
      );

      // Get date information for updating daily and monthly records
      const dateObj = moment(existingRecord.date, 'YYYY-MM-DD');
      const month = dateObj.month() + 1;
      const year = dateObj.year();

      // Update daily average record
      await this.userMealUtilsService.updateDailyRecord(
        user,
        existingRecord.date,
        month,
        year,
        session,
      );

      // Update daily total record

      await this.userMealUtilsService.updateDailyTotalRecord(
        user,
        existingRecord.date,
        month,
        year,
        session,
      );

      // Update monthly record
      await this.userMealUtilsService.updateMonthlyRecord(
        user,
        month,
        year,
        session,
      );

      await session.commitTransaction();

      const resp = MealRecordDTO.transform(updatedMealRecord);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Meal record updated successfully !!',
        data: resp,
      };
    } catch (error) {
      await session.abortTransaction();

      throw error;
    } finally {
      session.endSession();
    }
  }

  async deleteMealRecord(
    user: User,
    mealRecordId: string,
  ): Promise<DeleteMealRecordResDTO> {
    if (!Types.ObjectId.isValid(mealRecordId)) {
      throw new BadRequestException('Invalid record id provided !!');
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      // Find the meal record to delete
      const mealRecord = await this.mealRecordModel
        .findOne({
          _id: mealRecordId,
          userId: user._id,
          isDeleted: false,
        })
        .session(session);

      if (!mealRecord) {
        throw new BadRequestException('No Record found !!');
      }

      // Soft delete the meal record
      mealRecord.isDeleted = true;
      await mealRecord.save({ session });

      // Get date information for updating daily and monthly records
      const dateObj = moment(mealRecord.date, 'YYYY-MM-DD');
      const month = dateObj.month() + 1;
      const year = dateObj.year();
      const date = mealRecord.date;

      // Update daily average record
      await this.userMealUtilsService.updateDailyRecord(
        user,
        date,
        month,
        year,
        session,
      );

      // Update daily total record
      await this.userMealUtilsService.updateDailyTotalRecord(
        user,
        date,
        month,
        year,
        session,
      );

      // Update monthly record
      await this.userMealUtilsService.updateMonthlyRecord(
        user,
        month,
        year,
        session,
      );

      await session.commitTransaction();

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Meal record deleted successfully !!',
      };
    } catch (error) {
      await session.abortTransaction();

      throw error;
    } finally {
      session.endSession();
    }
  }

  async getMealRecordAnalytics(
    user: User,
    queryFilter: GetUserAnalyticsQueryInterface,
  ): Promise<GetMealRecordsAnalyticsResDTO> {
    const { filter } = queryFilter;

    switch (filter) {
      case AnalyticsRecordFilter.WEEKLY:
        return this.userMealUtilsService.getWeeklyMealRecords(user);

      case AnalyticsRecordFilter.MONTHLY:
        return this.userMealUtilsService.getMonthlyMealRecords(user);

      case AnalyticsRecordFilter.HALF_YEARLY:
        return this.userMealUtilsService.getHalfYearlyMealRecords(user, 6);

      case AnalyticsRecordFilter.YEARLY:
        return this.userMealUtilsService.getHalfYearlyMealRecords(user, 12);

      default:
        throw new BadRequestException(
          `Invalid filter provided: filter Must be one of: ${Object.values(AnalyticsRecordFilter).join(', ')}`,
        );
    }
  }
}
