import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserWeightRecordDTO } from './UserWeightRecord.dto';

export class CreateUserWeightRecordsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'User weight record created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'User weight record data',
    type: () => UserWeightRecordDTO,
  })
  data: UserWeightRecordDTO;
}
