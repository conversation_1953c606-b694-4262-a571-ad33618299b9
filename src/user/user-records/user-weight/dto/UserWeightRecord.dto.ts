import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsNotEmpty, IsDate, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';
import { UserWeightRecords } from 'models/user-records/Weight-Records';

export class UserWeightRecordDTO {
  @ApiProperty({
    description: 'Id of weight record',
    example: '1',
  })
  @IsNotEmpty({ message: 'User is required' })
  id: string;

  @ApiProperty({ description: 'User weight in kilograms', example: 70.5 })
  @IsNumber({}, { message: 'Weight must be a valid number' })
  @IsNotEmpty({ message: 'Weight is required' })
  weight: number;

  @ApiProperty({ description: 'Date of sleep record', example: '2025-03-25' })
  @IsDateString()
  @IsNotEmpty()
  date: Date;

  @ApiProperty({
    description: 'Record creation timestamp',
    example: '2025-03-21T12:34:56.789Z',
  })
  @IsDate({ message: 'CreatedAt must be a valid date' })
  @Type(() => Date)
  createdAt: Date;

  @IsDate({ message: 'CreatedAt must be a valid date' })
  @Type(() => Date)
  updatedAt: Date;

  static transform(object: UserWeightRecords): UserWeightRecordDTO {
    const transformedObj = new UserWeightRecordDTO();

    transformedObj.id = object._id.toString();
    transformedObj.date = object.date;
    transformedObj.createdAt = new Date(object.createdAt);
    transformedObj.updatedAt = new Date(object.updatedAt);
    transformedObj.weight = object.weight;

    return transformedObj;
  }
}
