import { IsNumber, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserWeightRecordDTO } from './UserWeightRecord.dto';

export class UpdateWeightReqDto {
  @ApiPropertyOptional({
    description: 'Updated weight value',
    example: 70.5,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  weight: number;
}

export class UpdateWeightResDto extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Weight updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated weight record',
    type: UserWeightRecordDTO,
  })
  data: UserWeightRecordDTO;
}
