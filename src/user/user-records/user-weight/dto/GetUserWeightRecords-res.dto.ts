import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { UserWeightRecordDTO } from './UserWeightRecord.dto';

export class GetUserWeightRecordsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of weight records',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'List of user weight records',
    type: () => [UserWeightRecordDTO],
  })
  data: UserWeightRecordDTO[];
}
