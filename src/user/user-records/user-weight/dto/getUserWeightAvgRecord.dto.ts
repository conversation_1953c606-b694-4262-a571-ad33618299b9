import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { AverageWeightDTO } from './averageWeight.dto';

export class GetUserWeightAvgRecordDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of sleep records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'List of sleep records',
    type: [AverageWeightDTO],
  })
  data: AverageWeightDTO[];
}
