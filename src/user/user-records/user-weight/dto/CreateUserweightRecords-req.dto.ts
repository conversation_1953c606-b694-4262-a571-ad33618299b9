import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>N<PERSON>ber, IsNotEmpty, IsDateString, Max, Min } from 'class-validator';

export class CreateUserweightRecordsReqDTO {
  @ApiProperty({
    description: 'The weight of the user in kilograms',
    type: Number,
  })
  @IsNumber()
  @Min(5, {
    message:
      'The Weight you entered is outside the acceptable range (5-400 kg).',
  })
  @Max(400, {
    message:
      'The Weight you entered is outside the acceptable range (5-400 kg)',
  })
  @IsNotEmpty()
  weight: number;

  @ApiProperty({ description: 'Date of Weight record', example: '2025-03-25' })
  @IsDateString({}, { message: 'Please select a valid date.' })
  @IsNotEmpty({ message: 'Please select a date.' })
  date: string;
}
