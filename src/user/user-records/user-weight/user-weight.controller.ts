import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { UserWeightService } from './user-weight.service';
import {
  CreateUserweightRecordsReqDTO,
  GetUserWeightRecordsResDTO,
  CreateUserWeightRecordsResDTO,
  UpdateWeightResDto,
  UpdateWeightReqDto,
} from './dto';
import { GetUserWeightRecordsQueryInterface } from './interfaces';
import { ErrorResponse } from 'src/utils/responses';
import { GetUserAnalyticsQueryInterface } from 'src/common/interfaces';

@ApiTags('User Weight Records')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class UserweightController {
  constructor(private readonly userweightService: UserWeightService) {}

  @ApiResponse({
    status: 201,
    description: 'Added User weight records successfully.',
    type: CreateUserWeightRecordsResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response.',
    type: ErrorResponse,
  })
  @Post('/weight_records')
  async CreateWeightRecord(
    @Req() req: Request,
    @Body() createUserWeightRecordsReqDTO: CreateUserweightRecordsReqDTO,
  ) {
    const user = req['user'];

    return this.userweightService.createWeightRecord(
      user._id,
      createUserWeightRecordsReqDTO,
    );
  }

  @ApiResponse({
    status: 201,
    description: 'Fetched User weight records successfully.',
    type: GetUserWeightRecordsResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response.',
    type: ErrorResponse,
  })
  @Get('/weight_records')
  async GetUserWeightRecords(
    @Req() req: Request,
    @Query() query: GetUserWeightRecordsQueryInterface,
  ) {
    const user = req['user'];
    return this.userweightService.getUserWeightRecords(query, user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Weight record updated successfully',
    type: UpdateWeightResDto,
  })
  @Put('/weight_records/:id')
  async updateWeightRecord(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() updateDto: UpdateWeightReqDto,
  ): Promise<UpdateWeightResDto> {
    const user = req['user'];
    return this.userweightService.updateWeightRecord(user._id, id, updateDto);
  }

  //   ----------------------------------------------------------------------------------

  @ApiResponse({
    status: 201,
    description: 'Fetched User weight records successfully.',
    type: GetUserWeightRecordsResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response.',
    type: ErrorResponse,
  })
  @Get('/weight_analytics')
  async GetUserWeightAnalytics(
    @Req() req: Request,
    @Query() queryFilters: GetUserAnalyticsQueryInterface,
  ) {
    const user = req['user'];

    return this.userweightService.getUserWeightAnalytics(
      queryFilters,
      user._id,
    );
  }
}
