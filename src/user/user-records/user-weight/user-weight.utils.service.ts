import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { HttpStatus } from '@nestjs/common';
import * as moment from 'moment';
import {
  MonthlyWeightSummary,
  UserWeightRecords,
} from 'models/user-records/Weight-Records';
import {
  AverageWeightDTO,
  GetSingleUserWeightRecordsResDTO,
  UserWeightRecordDTO,
} from './dto';
import { User } from 'models/user';

@Injectable()
export class WeightRecordUtilsService {
  constructor(
    @InjectModel(UserWeightRecords.name)
    private readonly weightRecordModel: Model<UserWeightRecords>,
    @InjectModel(MonthlyWeightSummary.name)
    private readonly monthlyWeightSummaryModel: Model<MonthlyWeightSummary>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  // Helper function to calculate average weight
  private calculateAverageWeight(records: any[]): number | null {
    if (records.length === 0) return null;
    const totalWeight = records.reduce((sum, record) => sum + record.weight, 0);
    return Math.round(totalWeight / records.length);
  }

  //to calculate average monthly weight on create and update
  async updateMonthlyAverage(userId: string, date: Date) {
    // Fetch the user's time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // Adjust the provided date to the user's time zone
    const adjustedDate = moment.tz(date, userTimeZone);

    const month = adjustedDate.month() + 1; // Adjusted to user’s local month
    const year = adjustedDate.year();
    const monthKey = year * 100 + month; // Format: YYYYMM

    // Fetch all weight records for the month adjusted to user's time zone
    const startOfMonthInTZ = adjustedDate.clone().startOf('month').toDate();
    const endOfMonthInTZ = adjustedDate.clone().endOf('month').toDate();

    const monthlyRecords = await this.weightRecordModel.find({
      userId,
      date: {
        $gte: startOfMonthInTZ,
        $lte: endOfMonthInTZ,
      },
    });

    // Calculate the average weight for the month
    const averageWeight = this.calculateAverageWeight(monthlyRecords);

    // Upsert the monthly weight summary record
    await this.monthlyWeightSummaryModel.findOneAndUpdate(
      { userId, month: monthKey },
      { userId, month: monthKey, year, averageWeight },
      { upsert: true, new: true },
    );
  }

  // weekly weight records (grouped into 7-day segments)
  async getWeeklyWeightRecords(userId: string) {
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const today = moment.tz(userTimeZone).toDate();

    // Calculate the start of the week (7 days ago) in the user's time zone
    const oneWeekAgo = moment
      .tz(today, userTimeZone)
      .subtract(6, 'days')
      .startOf('day')
      .toDate();

    // Calculate the end of the week (current day) in the user's time zone
    const weekEndDate = moment.tz(today, userTimeZone).endOf('day').toDate();

    // Fetch existing weight records for the past week in the user's time zone
    const weightRecords = await this.weightRecordModel
      .find({
        userId,
        date: { $gte: oneWeekAgo, $lte: weekEndDate },
      })
      .lean();

    // Map records by date (YYYY-MM-DD) for the user’s local time zone
    const recordMap = new Map<string, number>();
    weightRecords.forEach((record) => {
      const dateKey = moment.tz(record.date, userTimeZone).format('YYYY-MM-DD');
      recordMap.set(dateKey, record.weight);
    });

    // Build the result array, including missing dates for the week
    const data = [];
    for (let i = 0; i < 7; i++) {
      const currentDate = moment.tz(oneWeekAgo, userTimeZone).add(i, 'days');
      const dateKey = currentDate.format('YYYY-MM-DD');

      data.push(
        AverageWeightDTO.transform({
          year: currentDate.format('YYYY'),
          period: dateKey,
          averageWeight: recordMap.get(dateKey) ?? null,
        }),
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: data.length,
      data,
    };
  }

  // Monthly weight records (grouped in 4-5 weeks segments)
  async getMonthlyWeightRecords(userId: string) {
    // Fetch the user's time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // Get the current date adjusted to the user's time zone
    const currentDate = moment.tz(userTimeZone).toDate();

    // Calculate the start of the month (30 days ago) in the user's time zone
    const oneMonthAgo = moment
      .tz(currentDate, userTimeZone)
      .subtract(30, 'days')
      .startOf('day')
      .toDate();

    // Calculate the end of the month (current day) in the user's time zone
    const monthEndDate = moment
      .tz(currentDate, userTimeZone)
      .endOf('day')
      .toDate();

    // Fetch weight records for the last month (in the user's time zone)
    const weightRecords = await this.weightRecordModel
      .find({
        userId,
        date: { $gte: oneMonthAgo, $lte: monthEndDate },
      })
      .lean();

    // Group records by week (1-5)
    const groupedData: Record<string, any[]> = {};
    for (let i = 1; i <= 5; i++) {
      groupedData[`week${i}`] = [];
    }

    weightRecords.forEach((record) => {
      const diffInDays = Math.ceil(
        (new Date(record.date).getTime() - oneMonthAgo.getTime()) /
          (1000 * 60 * 60 * 24),
      );
      const weekIndex = Math.ceil(diffInDays / 7);
      if (weekIndex >= 1 && weekIndex <= 5) {
        groupedData[`week${weekIndex}`].push(record);
      }
    });

    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    const responseRecords = Array.from({ length: 5 }, (_, index) => {
      const weekKey = `week${index + 1}`;
      const records = groupedData[weekKey];

      const averageWeight = this.calculateAverageWeight(records);

      // Adjust week start and end dates according to the user's time zone
      const weekStartDate = moment
        .tz(oneMonthAgo, userTimeZone)
        .add(index * 7, 'days')
        .startOf('day')
        .toDate();
      const weekEndDate = moment
        .tz(oneMonthAgo, userTimeZone)
        .add((index + 1) * 7 - 1, 'days')
        .endOf('day')
        .toDate();

      // Clamp the end date to the current date if it exceeds it
      if (weekEndDate > currentDate) {
        weekEndDate.setTime(currentDate.getTime());
      }

      return AverageWeightDTO.transform({
        year: new Date().getFullYear().toString(),
        period: `week_${index + 1}`,
        averageWeight: averageWeight ?? null,
        startDate: formatDate(weekStartDate),
        endDate: formatDate(weekEndDate),
      });
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  //   // Half-Yearly weight records (grouped into 18-day segments)
  async getHalfYearlyWeightRecords(userId: string) {
    // Fetch the user's time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const recordsMap = new Map();

    // Generate keys for the last 6 months (including the current month)
    for (let i = 5; i >= 0; i--) {
      // Create a new moment for each iteration to avoid mutation issues
      const date = moment
        .tz(userTimeZone)
        .subtract(i, 'months')
        .startOf('month')
        .toDate();

      const monthKey = date.getFullYear() * 100 + (date.getMonth() + 1);

      // Use the date directly with toLocaleString
      const monthLabel = date.toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      });
      const [monthName, yearStr] = monthLabel.split(' ');

      recordsMap.set(monthKey, {
        year: yearStr,
        period: monthName,
        averageWeight: null, // Default to null
      });
    }

    // Fetch stored monthly averages for the last 6 months
    const records = await this.monthlyWeightSummaryModel
      .find({
        userId,
        month: {
          $gte: [...recordsMap.keys()][0],
          $lte: [...recordsMap.keys()][5],
        },
      })
      .lean();

    // Fill the recordsMap with actual data
    records.forEach(({ month, year, averageWeight }) => {
      if (recordsMap.has(month)) {
        // Create the date properly for formatting
        const monthDate = moment.tz(
          `${year}-${String(month % 100).padStart(2, '0')}-01`,
          'YYYY-MM-DD',
          userTimeZone,
        );

        recordsMap.set(month, {
          year: year.toString(),
          period: monthDate.format('MMMM'),
          averageWeight,
        });
      }
    });

    // Transform data using DTO and return response
    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      AverageWeightDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  //   // Yearly weight records (grouped into 30-day segments)
  async getYearlyWeightRecords(userId: string) {
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const currentDate = moment.tz(userTimeZone).toDate();
    const recordsMap = new Map();

    // Generate keys for the last 12 months (including the current month)
    for (let i = 11; i >= 0; i--) {
      const date = moment
        .tz(currentDate, userTimeZone)
        .subtract(i, 'months')
        .startOf('month')
        .toDate();

      // Use moment to format the date to ISO string format before passing it to moment
      const formattedDate = moment(date).format('YYYY-MM-DD');

      const monthKey = parseInt(moment(formattedDate).format('YYYYMM'), 10);
      const monthLabel = moment(formattedDate).format('MMMM YYYY');
      const [monthName, yearStr] = monthLabel.split(' ');

      recordsMap.set(monthKey, {
        year: yearStr,
        period: monthName,
        averageWeight: null, // Default to null (or 0 if you prefer)
      });
    }

    // Fetch stored monthly averages for the last 12 months
    const records = await this.monthlyWeightSummaryModel
      .find({
        userId,
        month: {
          $gte: [...recordsMap.keys()][0],
          $lte: [...recordsMap.keys()][11],
        },
      })
      .lean();

    // Fill the recordsMap with actual data
    records.forEach(({ month, year, averageWeight }) => {
      if (recordsMap.has(month)) {
        recordsMap.set(month, {
          year: year.toString(),
          period: moment
            .tz(
              `${year}-${String(month % 100).padStart(2, '0')}-01`,
              userTimeZone,
            )
            .format('MMMM'),
          averageWeight,
        });
      }
    });

    // Transform data using DTO and return response
    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      AverageWeightDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async getLastWeightLogIn7Days(
    userId: string,
  ): Promise<GetSingleUserWeightRecordsResDTO> {
    // Fetch user and time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // Get start of today in user's local time zone and convert it to UTC
    const startOfDayInTZ = moment.tz(userTimeZone).startOf('day');
    const endOfDayInTZ = moment.tz(userTimeZone).endOf('day');

    const startOfDayUTC = startOfDayInTZ.clone().utc().toDate();
    const endOfDayUTC = endOfDayInTZ.clone().utc().toDate();

    // Find records for today
    let records = await this.weightRecordModel
      .find({ userId, date: { $gte: startOfDayUTC, $lte: endOfDayUTC } })
      .sort({ date: -1 });

    if (records.length === 0) {
      // If no records found today, check the last 7 days
      const sevenDaysAgoInTZ = moment
        .tz(userTimeZone)
        .subtract(7, 'days')
        .startOf('day');
      const sevenDaysAgoUTC = sevenDaysAgoInTZ.clone().utc().toDate();

      records = await this.weightRecordModel
        .find({ userId, date: { $gte: sevenDaysAgoUTC, $lte: endOfDayUTC } })
        .sort({ date: -1 });

      if (records.length === 0) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          data: null,
        };
      }
    }

    // Retrieve the most recent record from the filtered data
    const dateFilter = { date: records[0].date };

    const weightRecords = await this.weightRecordModel
      .find({ userId, ...dateFilter })
      .sort({ createdAt: -1 })
      .exec();

    const resp = UserWeightRecordDTO.transform(weightRecords[0]);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
