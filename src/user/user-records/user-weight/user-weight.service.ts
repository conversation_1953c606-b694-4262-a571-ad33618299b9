import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CreateUserweightRecordsReqDTO,
  GetUserWeightRecordsResDTO,
  CreateUserWeightRecordsResDTO,
  UpdateWeightReqDto,
  UpdateWeightResDto,
  GetUserWeightAvgRecordDTO,
  GetSingleUserWeightRecordsResDTO,
  UserWeightRecordDTO,
} from './dto';
import { UserWeightRecords } from 'models/user-records/Weight-Records';
import { WeightRecordUtilsService } from './user-weight.utils.service';
import { GetUserWeightRecordsQueryInterface } from './interfaces';
import {
  AnalyticsRecordFilter,
  GetUserAnalyticsQueryInterface,
} from 'src/common/interfaces';
import * as moment from 'moment-timezone';
import { User } from 'models/user';

@Injectable()
export class UserWeightService {
  constructor(
    @InjectModel(UserWeightRecords.name)
    private readonly userWeightRecordsModel: Model<UserWeightRecords>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    private readonly WeightRecordUtilsService: WeightRecordUtilsService,
  ) {}

  async createWeightRecord(
    userId: string,
    recordData: CreateUserweightRecordsReqDTO,
  ): Promise<CreateUserWeightRecordsResDTO> {
    // Fetch user and time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // Convert input date to moment object in user time zone
    const inputDateInTZ = moment
      .tz(recordData.date, userTimeZone)
      .startOf('day');
    const todayInTZ = moment.tz(userTimeZone).startOf('day');

    // Prevent adding records for future dates in user time zone
    if (inputDateInTZ.isAfter(todayInTZ)) {
      throw new BadRequestException(
        `You can only add weight records for past or current dates. Future dates are not allowed.`,
      );
    }

    // Convert the start and end of the user's local day to UTC
    const startOfDayUTC = inputDateInTZ.clone().utc().toDate();
    const endOfDayUTC = inputDateInTZ.clone().endOf('day').utc().toDate();

    // Check for existing record on the same user-local day
    const existingRecord = await this.userWeightRecordsModel.findOne({
      userId,
      date: {
        $gte: startOfDayUTC,
        $lte: endOfDayUTC,
      },
    });

    if (existingRecord) {
      throw new BadRequestException(
        `You have already added a weight record for ${inputDateInTZ.format('YYYY-MM-DD')} (${userTimeZone}).`,
      );
    }

    // Create new weight record (store date in UTC)
    const newRecord = await this.userWeightRecordsModel.create({
      userId,
      weight: recordData.weight,
      date: moment.tz(recordData.date, userTimeZone).utc().toDate(), // store in UTC
    });

    // Update monthly average based on inputDate in user TZ
    await this.WeightRecordUtilsService.updateMonthlyAverage(
      userId,
      inputDateInTZ.toDate(),
    );

    // Transform and return response
    const weightRecordResp = UserWeightRecordDTO.transform(newRecord);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Weight Record Added Successfully!',
      data: weightRecordResp,
    };
  }

  async getUserWeightAnalytics(
    queryFilters: GetUserAnalyticsQueryInterface,
    userId: string,
  ): Promise<GetUserWeightRecordsResDTO | GetUserWeightAvgRecordDTO> {
    const { filter } = queryFilters;

    switch (filter) {
      case AnalyticsRecordFilter.WEEKLY:
        return this.WeightRecordUtilsService.getWeeklyWeightRecords(userId);

      case AnalyticsRecordFilter.MONTHLY:
        return this.WeightRecordUtilsService.getMonthlyWeightRecords(userId);

      case AnalyticsRecordFilter.HALF_YEARLY:
        return this.WeightRecordUtilsService.getHalfYearlyWeightRecords(userId);

      case AnalyticsRecordFilter.YEARLY:
        return this.WeightRecordUtilsService.getYearlyWeightRecords(userId);

      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }

  async updateWeightRecord(
    userId: string,
    weightId: string,
    updateDto: UpdateWeightReqDto,
  ): Promise<UpdateWeightResDto> {
    const existingRecord = await this.userWeightRecordsModel.findOne({
      _id: weightId,
      userId,
    });

    if (!existingRecord) {
      throw new BadRequestException('Weight record not found.');
    }

    // Fetch the user's time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // If you plan to use the user's timezone in future date comparison or other logic:
    const existingRecordDateInTZ = moment
      .tz(existingRecord.date, userTimeZone)
      .startOf('day'); // Adjusts to the start of the day in the user's time zone

    // Update the weight record
    const updatedRecord = await this.userWeightRecordsModel.findByIdAndUpdate(
      weightId,
      updateDto,
      {
        new: true,
        runValidators: true,
      },
    );

    // Update monthly average weight
    await this.WeightRecordUtilsService.updateMonthlyAverage(
      userId,
      existingRecordDateInTZ.toDate(),
    );

    const resp = UserWeightRecordDTO.transform(updatedRecord);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated weight record successfully!',
      data: resp,
    };
  }

  async getUserWeightRecords(
    queryFilter: GetUserWeightRecordsQueryInterface,
    userId: string,
  ): Promise<GetSingleUserWeightRecordsResDTO> {
    const { date } = queryFilter;

    if (date) {
      // Fetch user and time zone
      const user = await this.userModel.findById(userId);
      const userTimeZone = user?.timeZone || 'UTC';

      // Convert date to user's time zone (start of the day)
      const inputDateInTZ = moment.tz(date.trim(), userTimeZone).startOf('day');

      // Convert user's local start of the day to UTC
      const startOfDayUTC = inputDateInTZ.clone().utc().toDate();
      const endOfDayUTC = inputDateInTZ.clone().endOf('day').utc().toDate();

      // Query for the weight record on the given date range (start and end of day in UTC)
      const record = await this.userWeightRecordsModel
        .findOne({
          userId,
          date: {
            $gte: startOfDayUTC,
            $lte: endOfDayUTC,
          },
        })
        .sort({ createdAt: -1 });

      if (!record) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          data: null,
        };
      }

      const resp = UserWeightRecordDTO.transform(record);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: resp,
      };
    } else {
      return this.WeightRecordUtilsService.getLastWeightLogIn7Days(userId);
    }
  }
}
