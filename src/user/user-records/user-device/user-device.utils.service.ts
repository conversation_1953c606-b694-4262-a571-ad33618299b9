import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import {
  DeviceUsageSession,
  UserDeviceRecord,
  MonthlyDeviceUsageSummary,
} from 'models/user-records/Device-records';

import { DeviceUsageAverageDTO } from './dto';
import { User } from 'models/user';
import * as moment from 'moment-timezone';

@Injectable()
export class DeviceUsageUtilsService {
  constructor(
    @InjectModel(UserDeviceRecord.name)
    private readonly userDeviceRecordModel: Model<UserDeviceRecord>,

    @InjectModel(MonthlyDeviceUsageSummary.name)
    private readonly monthlyDeviceUsageModel: Model<MonthlyDeviceUsageSummary>,

    @InjectModel(DeviceUsageSession.name)
    private readonly deviceUsageSessionModel: Model<DeviceUsageSession>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  // Calculates average device usage time (in minutes)
  private calculateAverageUsage(records: any[]): number | null {
    if (!records.length) return null;

    const totalDuration = records.reduce(
      (sum, record) => sum + (record.totalUsageTime || 0),
      0,
    );

    return Math.round(totalDuration / records.length);
  }

  // Updates the monthly average when a device usage record is created or updated.

  async updateMonthlyAverage(userId: string, date: Date): Promise<void> {
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    const monthStart = new Date(year, month - 1, 1);
    const monthEnd = new Date(year, month, 0, 23, 59, 59, 999);

    // Fetch DAILY summaries for the month (NOT individual sessions!)
    const dailySummaries = await this.userDeviceRecordModel.find({
      userId,
      utcDate: {
        $gte: monthStart,
        $lte: monthEnd,
      },
      isDeleted: false,
    });

    const average = this.calculateAverageUsage(dailySummaries);

    await this.monthlyDeviceUsageModel.findOneAndUpdate(
      { userId, month, year },
      {
        $set: {
          userId,
          month,
          year,
          averageDeviceUsage: average,
        },
      },
      { upsert: true, new: true },
    );
  }

  // for device Analytics-----------------------------------------------

  // Weekly average device usage (last 7 days)
  async getWeeklyDeviceUsageRecords(userId: string) {
    // 1️⃣ Get user's timezone
    const user = await this.userModel.findById(userId).lean();
    const userTimeZone = user?.timeZone || 'UTC';

    // 2️⃣ Compute user's local today (YYYY-MM-DD)
    const todayLocal = moment.tz(userTimeZone).startOf('day');

    // 3️⃣ Compute 6 days ago (local)
    const localStart = todayLocal.clone().subtract(6, 'days').startOf('day');

    // 4️⃣ Build localDate strings for all 7 days
    const localDates = [];
    for (let i = 0; i < 7; i++) {
      localDates.push(localStart.clone().add(i, 'days').format('YYYY-MM-DD'));
    }

    // 5️⃣ Fetch records by localDate strings directly (no UTC needed)
    const usageRecords = await this.userDeviceRecordModel
      .find({
        userId,
        localDate: { $in: localDates },
        isDeleted: false,
      })
      .lean();

    // 6️⃣ Map by localDate string (no timezone conversion needed!)
    const recordMap = new Map<string, any>();
    usageRecords.forEach((record) => {
      recordMap.set(record.localDate, record);
    });

    // 7️⃣ Build the result array in the requested order
    const results = [];
    localDates.forEach((dateKey) => {
      const currentLocalDate = moment.tz(dateKey, 'YYYY-MM-DD', userTimeZone);
      const record = recordMap.get(dateKey);

      results.push(
        DeviceUsageAverageDTO.transform({
          year: currentLocalDate.year(),
          period: dateKey,
          averageDeviceUsage: record?.totalUsageTime ?? null,
        }),
      );
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: results.length,
      data: results,
    };
  }
  // Monthly average device usage (grouped into 5 weeks max)
  async getMonthlyDeviceUsageRecords(userId: string) {
    const currentDate = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setDate(currentDate.getDate() - 30);
    oneMonthAgo.setHours(0, 0, 0, 0);

    const monthEndDate = new Date();
    monthEndDate.setHours(23, 59, 59, 999);

    const usageRecords = await this.userDeviceRecordModel
      .find({
        userId,
        utcDate: { $gte: oneMonthAgo, $lte: monthEndDate },
        isDeleted: false,
      })
      .lean();

    const groupedData: Record<string, any[]> = {};
    for (let i = 1; i <= 5; i++) {
      groupedData[`week${i}`] = [];
    }

    usageRecords.forEach((record) => {
      const diffInDays = Math.ceil(
        (new Date(record.utcDate).getTime() - oneMonthAgo.getTime()) /
          (1000 * 60 * 60 * 24),
      );
      const weekIndex = Math.ceil(diffInDays / 7);
      if (weekIndex >= 1 && weekIndex <= 5) {
        groupedData[`week${weekIndex}`].push(record);
      }
    });

    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    const responseRecords = Array.from({ length: 5 }, (_, index) => {
      const weekKey = `week${index + 1}`;
      const records = groupedData[weekKey];
      const avgUsage = this.calculateAverageUsage(records);

      const weekStartDate = new Date(oneMonthAgo);
      weekStartDate.setDate(oneMonthAgo.getDate() + index * 7);
      weekStartDate.setHours(0, 0, 0, 0);

      const weekEndDate = new Date(oneMonthAgo);
      weekEndDate.setDate(oneMonthAgo.getDate() + (index + 1) * 7 - 1);
      weekEndDate.setHours(23, 59, 59, 999);

      if (weekEndDate > currentDate) {
        weekEndDate.setTime(currentDate.getTime());
      }

      return DeviceUsageAverageDTO.transform({
        year: new Date().getFullYear(),
        period: `week_${index + 1}`,
        averageDeviceUsage: avgUsage ?? null,
        startDate: formatDate(weekStartDate),
        endDate: formatDate(weekEndDate),
      });
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  //Half yearly device usage record
  async getHalfYearlyDeviceUsage(userId: string) {
    const currentDate = new Date();
    const recordsMap = new Map();

    const monthYearPairs: { year: number; month: number }[] = [];

    // Generate last 6 month keys
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(currentDate.getMonth() - i, 1);
      const month = date.getMonth() + 1;
      const year = date.getFullYear();

      const monthKey = `${year}-${month}`;
      const period = date.toLocaleString('default', { month: 'long' });

      recordsMap.set(monthKey, {
        year,
        period,
        averageDeviceUsage: null,
      });

      monthYearPairs.push({ year, month });
    }

    // Build $or query for all year-month combinations
    const orQuery = monthYearPairs.map(({ year, month }) => ({
      userId,
      year,
      month,
    }));

    const records = await this.monthlyDeviceUsageModel
      .find({ $or: orQuery })
      .lean();

    records.forEach(({ year, month, averageDeviceUsage }) => {
      const monthKey = `${year}-${month}`;
      const period = new Date(`${year}-${month}-01`).toLocaleString('default', {
        month: 'long',
      });

      if (recordsMap.has(monthKey)) {
        recordsMap.set(monthKey, {
          year,
          period,
          averageDeviceUsage,
        });
      }
    });

    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      DeviceUsageAverageDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  // yearly device usage

  async getYearlyDeviceUsage(userId: string) {
    const currentDate = new Date();
    const recordsMap = new Map();

    const monthYearPairs: { year: number; month: number }[] = [];

    // Generate last 12 month keys
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(currentDate.getMonth() - i, 1);

      const month = date.getMonth() + 1;
      const year = date.getFullYear();
      const monthKey = `${year}-${month}`;
      const period = date.toLocaleString('default', { month: 'long' });

      recordsMap.set(monthKey, {
        year,
        period,
        averageDeviceUsage: null,
      });

      monthYearPairs.push({ year, month });
    }

    // Query all 12 months
    const orQuery = monthYearPairs.map(({ year, month }) => ({
      userId,
      year,
      month,
    }));

    const records = await this.monthlyDeviceUsageModel
      .find({ $or: orQuery })
      .lean();

    // Fill recordsMap with actual data
    records.forEach(({ year, month, averageDeviceUsage }) => {
      const monthKey = `${year}-${month}`;
      const period = new Date(`${year}-${month}-01`).toLocaleString('default', {
        month: 'long',
      });

      if (recordsMap.has(monthKey)) {
        recordsMap.set(monthKey, {
          year,
          period,
          averageDeviceUsage,
        });
      }
    });

    // Transform and return
    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      DeviceUsageAverageDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async recalculateTotalUsageForDate(
    userId: string,
    date: Date,
  ): Promise<UserDeviceRecord> {
    // 1️⃣ Get user's timezone
    const user = await this.userModel.findById(userId).lean();
    const userTimeZone = user?.timeZone || 'UTC';

    // 2️⃣ Compute user's local date string: "YYYY-MM-DD"
    const localDateString = moment.tz(date, userTimeZone).format('YYYY-MM-DD');

    // 3️⃣ Compute UTC start and end for finding sessions
    const userStartOfDay = moment
      .tz(localDateString, 'YYYY-MM-DD', userTimeZone)
      .startOf('day');
    const userEndOfDay = userStartOfDay.clone().add(1, 'day');

    const startOfDayUTC = userStartOfDay.clone().utc().toDate();
    const endOfDayUTC = userEndOfDay.clone().utc().toDate();

    const sessions = await this.deviceUsageSessionModel.find({
      userId,
      isDeleted: false,
      startTime: { $gte: startOfDayUTC, $lt: endOfDayUTC },
    });

    const totalUsageTime = sessions.reduce(
      (sum, session) => sum + (session.durationConsumed || 0),
      0,
    );

    // 5️⃣ Upsert summary: use string localDate as unique key
    const updatedSummary = await this.userDeviceRecordModel.findOneAndUpdate(
      {
        userId,
        localDate: localDateString,
        isDeleted: false,
      },
      {
        $set: {
          totalUsageTime,
          localDate: localDateString,
          utcDate: startOfDayUTC,
        },
      },
      {
        upsert: true,
        new: true,
      },
    );

    return updatedSummary;
  }
}
