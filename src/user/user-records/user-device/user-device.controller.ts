import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiTags,
  ApiBearerAuth,
  ApiResponse,
  ApiOperation,
  ApiParam,
} from '@nestjs/swagger';

import { AuthGuard } from 'src/middlewares';
import { DeviceUsageService } from './user-device.service';

import {
  CreateDeviceUsageRecordResDTO,
  CreateDeviceUsageSessionDto,
} from './dto/createDeviceRecord.dto';
import {
  AddMoreDeviceUsageRecordResDTO,
  AddMoreDeviceUsageSessionDto,
  GetDeviceUsageSessionResDTO,
  GetMonthlyUserDeviceUsageAvgRecordDTO,
  GetWeeklyDeviceUsageAvgResDTO,
  SyncUpdateDeviceUsageSessionDto,
  SyncUpdateDeviceUsageSessionResDTO,
  UpdateAddMoreDeviceUsageRecordResDTO,
  UpdateAddMoreDeviceUsageSessionDto,
  UpdateSessionEndDto,
  UpdateSessionEndResDto,
} from './dto';
import { GetDeviceUsageSessionQueryInterface } from './interface';
import { GetUserAnalyticsQueryInterface } from 'src/common/interfaces';

@ApiTags('Device-Usage')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class DeviceUsageController {
  constructor(private readonly deviceUsageService: DeviceUsageService) {}

  @ApiOperation({ summary: 'Create a new device usage session' })
  @ApiResponse({
    status: 201,
    description: 'Device usage session successfully created',
    type: CreateDeviceUsageRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input or session creation failed',
  })
  @Post('/device_timer')
  async CreateDeviceUsageRecordSession(
    @Req() req: Request,
    @Body() body: CreateDeviceUsageSessionDto,
  ): Promise<CreateDeviceUsageRecordResDTO> {
    const user = req['user'];
    return this.deviceUsageService.createDeviceUsageRecordSession(
      user._id,
      body,
    );
  }

  @ApiOperation({ summary: 'Update a device usage session' })
  @ApiParam({ name: 'id', description: 'Device session ID' })
  @ApiResponse({
    status: 200,
    description: 'Device session successfully updated',
    type: SyncUpdateDeviceUsageSessionResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Device session not found',
  })
  @Put('/device_timer/:id')
  async SyncUpdateDeviceUsageSession(
    @Body() updateData: SyncUpdateDeviceUsageSessionDto,
    @Param('id') id: string,
  ): Promise<SyncUpdateDeviceUsageSessionResDTO> {
    return this.deviceUsageService.syncUpdateDeviceUsageSession(updateData, id);
  }

  @ApiOperation({ summary: 'Mark the end of a device usage session' })
  @ApiParam({ name: 'id', description: 'Device session ID to end' })
  @ApiResponse({
    status: 200,
    description: 'Device usage session ended successfully',
    type: UpdateSessionEndResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Failed to end session due to invalid data or state',
  })
  @Put('/device_timer/end/:id')
  async DeviceUsageSessionEnd(
    @Param('id') id: string,
    @Body() updateData: UpdateSessionEndDto,
    @Req() req: Request,
  ): Promise<UpdateSessionEndResDto> {
    const user = req['user'];
    return this.deviceUsageService.deviceUsageSessionEnd(
      id,
      user._id,
      updateData,
    );
  }

  @ApiOperation({ summary: 'Get device usage sessions by date range' })
  @ApiResponse({
    status: 200,
    description: 'Successfully fetched device usage sessions',
    type: GetDeviceUsageSessionResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid query parameters or user data',
  })
  @Get('/device_timer')
  async GetDeviceUsageSessionByDate(
    @Query() queryFilters: GetDeviceUsageSessionQueryInterface,
    @Req() req: Request,
  ): Promise<GetDeviceUsageSessionResDTO> {
    const user = req['user'];
    return this.deviceUsageService.getDeviceUsageSessionByDate(
      queryFilters,
      user._id,
    );
  }

  @ApiOperation({
    summary: 'Add more records to an ongoing device usage session',
  })
  @ApiResponse({
    status: 201,
    description: 'Additional usage records added successfully',
    type: AddMoreDeviceUsageRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Failed to add additional usage records',
  })
  @Post('/device_timer/add_more')
  async AddDeviceUsageSessionManually(
    @Req() req: Request,
    @Body() body: AddMoreDeviceUsageSessionDto,
  ): Promise<AddMoreDeviceUsageRecordResDTO> {
    const user = req['user'];

    return this.deviceUsageService.addDeviceUsageSessionManually(
      user._id,
      body,
    );
  }

  @ApiOperation({
    summary: 'Update additional records of a device usage session',
  })
  @ApiParam({ name: 'id', description: 'Device session ID to update' })
  @ApiResponse({
    status: 200,
    description: 'Additional device usage records successfully updated',
    type: UpdateAddMoreDeviceUsageRecordResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Session not found or update failed',
  })
  @Put('/device_timer/add_more/:id')
  async UpdateAddMoreDeviceUsageRecordById(
    @Param('id') id: string,
    @Req() req: Request,
    @Body() body: UpdateAddMoreDeviceUsageSessionDto,
  ): Promise<UpdateAddMoreDeviceUsageRecordResDTO> {
    const user = req['user'];
    return this.deviceUsageService.updateAddMoreDeviceUsageRecordById(
      id,
      user._id,
      body,
    );
  }

  @ApiOperation({ summary: 'Get device usage analytics (weekly/monthly)' })
  @ApiResponse({
    status: 200,
    description: 'Successfully fetched device usage analytics',
    type: GetWeeklyDeviceUsageAvgResDTO, // For weekly
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully fetched monthly device usage analytics',
    type: GetMonthlyUserDeviceUsageAvgRecordDTO, // For monthly
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid query parameters or user data',
  })
  @Get('/device_timer_analytics')
  async GetUserDeviceRecords(
    @Query() queryFilters: GetUserAnalyticsQueryInterface,
    @Req() req: Request,
  ): Promise<GetMonthlyUserDeviceUsageAvgRecordDTO> {
    const user = req['user'];
    return this.deviceUsageService.getUserDeviceAnalytics(
      queryFilters,
      user._id,
    );
  }
}
