import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  DeviceUsageSession,
  UserDeviceRecord,
} from 'models/user-records/Device-records';
import { CreateDeviceUsageSessionDto } from './dto/createDeviceRecord.dto';
import { CreateDeviceUsageRecordResDTO } from './dto/createDeviceRecord.dto';
import {
  AddMoreDeviceUsageRecordResDTO,
  AddMoreDeviceUsageSessionDto,
  DeviceUsageSessionDto,
  DeviceUsageSummaryDto,
  GetDeviceUsageSessionResDTO,
  GetMonthlyUserDeviceUsageAvgRecordDTO,
  SyncUpdateDeviceUsageSessionDto,
  SyncUpdateDeviceUsageSessionResDTO,
  UpdateAddMoreDeviceUsageRecordResDTO,
  UpdateAddMoreDeviceUsageSessionDto,
  UpdateSessionEndDto,
  UpdateSessionEndResDto,
} from './dto';
import { User } from 'models/user';
import { GetDeviceUsageSessionQueryInterface } from './interface';
import { DeviceUsageUtilsService } from './user-device.utils.service';
import {
  AnalyticsRecordFilter,
  GetUserAnalyticsQueryInterface,
} from 'src/common/interfaces';
import { DateTime } from 'luxon';

@Injectable()
export class DeviceUsageService {
  constructor(
    @InjectModel(DeviceUsageSession.name)
    private readonly deviceUsageSessionModel: Model<DeviceUsageSession>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,

    @InjectModel(UserDeviceRecord.name)
    private readonly userDeviceRecordModel: Model<UserDeviceRecord>,

    private readonly deviceUsageUtilsService: DeviceUsageUtilsService,
  ) {}

  async createDeviceUsageRecordSession(
    userId: string,
    recordData: CreateDeviceUsageSessionDto,
  ): Promise<CreateDeviceUsageRecordResDTO> {
    const { startTime, durationSet } = recordData;

    const usageTime = new Date(startTime);
    const currentTime = new Date();

    if (usageTime > currentTime) {
      throw new BadRequestException(
        'Cannot create a session with a future start time.',
      );
    }

    const existingSession = await this.deviceUsageSessionModel.findOne({
      userId,
      startTime: usageTime,
      isDeleted: false,
    });

    if (existingSession) {
      throw new BadRequestException(
        'A device usage session already exists for this time.',
      );
    }

    const endTime = new Date(usageTime.getTime() + durationSet * 60000);

    const newSession = await this.deviceUsageSessionModel.create({
      userId,
      startTime: usageTime,
      durationSet,
      endTime,
    });

    // // Normalize usage date to midnight UTC
    const usageDate = new Date(usageTime);
    usageDate.setUTCHours(0, 0, 0, 0); // normalize to 00:00:00.000 UTC

    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    const resp = DeviceUsageSessionDto.transform(newSession);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device usage record created successfully',
      data: resp,
    };
  }

  async syncUpdateDeviceUsageSession(
    updateData: SyncUpdateDeviceUsageSessionDto,
    id: string,
  ): Promise<SyncUpdateDeviceUsageSessionResDTO> {
    const { durationConsumed } = updateData;

    const session = await this.deviceUsageSessionModel.findOne({
      _id: id,
      isSessionExpired: false,
    });

    if (!session || session.isDeleted) {
      throw new BadRequestException('Device usage session not found.');
    }

    if (durationConsumed !== undefined) {
      session.durationConsumed = durationConsumed;
    }

    await session.save();

    const usageDate = new Date(session.startTime);
    usageDate.setUTCHours(0, 0, 0, 0);

    const nextDate = new Date(usageDate);
    nextDate.setUTCDate(nextDate.getUTCDate() + 1);

    const resp = DeviceUsageSessionDto.transform(session);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device usage session updated successfully',
      data: resp,
    };
  }

  async deviceUsageSessionEnd(
    id: string,
    userId: string,
    updateData: UpdateSessionEndDto,
  ): Promise<UpdateSessionEndResDto> {
    const { durationConsumed, endTime } = updateData;

    const session = await this.deviceUsageSessionModel.findOne({
      _id: id,
      userId,
      isDeleted: false,
      isSessionExpired: false,
    });

    if (!session) {
      throw new BadRequestException('Device usage session not found.');
    }

    // Update session details
    session.endTime = new Date(endTime);
    session.isSessionExpired = true;
    session.durationConsumed = durationConsumed;
    session.startTime = new Date(session.startTime); // Ensure correct Date type
    await session.save();

    // Use utility to recalculate the total usage for that day
    const updatedSummary =
      await this.deviceUsageUtilsService.recalculateTotalUsageForDate(
        userId,
        session.startTime,
      );

    // Recalculate monthly average as well
    await this.deviceUsageUtilsService.updateMonthlyAverage(
      userId,
      session.startTime,
    );

    const resp = DeviceUsageSummaryDto.transform(updatedSummary, [session]);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device usage session ended and summary updated.',
      data: resp,
    };
  }

  async getDeviceUsageSessionByDate(
    queryFilter: GetDeviceUsageSessionQueryInterface,
    userId: string,
  ): Promise<GetDeviceUsageSessionResDTO> {
    const user = await this.userModel.findById(userId);
    const timezone = user?.timeZone || 'UTC';

    let userDate = DateTime.now().setZone(timezone);

    if (queryFilter.date) {
      userDate = DateTime.fromISO(queryFilter.date.trim(), { zone: timezone });
      if (!userDate.isValid) {
        throw new BadRequestException('Invalid date format.');
      }
    }

    // Get UTC start and end for that user day
    const startOfDayUTC = userDate.startOf('day').toUTC().toJSDate();
    const endOfDayUTC = userDate.endOf('day').toUTC().toJSDate();

    const record = await this.deviceUsageSessionModel
      .find({
        userId,
        startTime: { $gte: startOfDayUTC, $lte: endOfDayUTC },
        isDeleted: false,
        isSessionExpired: true,
        durationConsumed: { $gt: 0 },
      })
      .sort({ createdAt: -1 });

    const result = record.map((item) =>
      DeviceUsageSessionDto.transform(item, timezone),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: result,
    };
  }

  async addDeviceUsageSessionManually(
    userId: string,
    requestData: AddMoreDeviceUsageSessionDto,
  ): Promise<AddMoreDeviceUsageRecordResDTO> {
    const { startTime, durationConsumed } = requestData;

    console.log('start time :', startTime);

    const start = new Date(startTime);
    const end = new Date(start.getTime() + durationConsumed * 60000);

    const session = await this.deviceUsageSessionModel.create({
      userId,
      startTime: start,
      endTime: end,
      durationConsumed,
      durationSet: durationConsumed,
      isSessionExpired: true,
      isDeleted: false,
    });

    // Use updated utility method to recalculate and fetch updated summary

    const updatedSummary =
      await this.deviceUsageUtilsService.recalculateTotalUsageForDate(
        userId,
        start,
      );

    // Update monthly average
    await this.deviceUsageUtilsService.updateMonthlyAverage(userId, start);

    const resp = DeviceUsageSummaryDto.transform(updatedSummary, [session]);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device usage session created and summary updated.',
      data: resp,
    };
  }

  async updateAddMoreDeviceUsageRecordById(
    id: string,
    userId: string,
    body: UpdateAddMoreDeviceUsageSessionDto,
  ): Promise<UpdateAddMoreDeviceUsageRecordResDTO> {
    const { startTime, durationConsumed } = body;

    const session = await this.deviceUsageSessionModel.findOne({
      _id: id,
      userId,
      isDeleted: false,
    });

    if (!session) {
      throw new BadRequestException('Active session not found for update.');
    }

    session.startTime = new Date(startTime);
    session.durationConsumed = durationConsumed;
    session.endTime = new Date(
      session.startTime.getTime() + durationConsumed * 60000,
    );

    await session.save();

    const sessionDate = new Date(session.startTime);
    sessionDate.setHours(0, 0, 0, 0); // normalize date

    const updatedSummary =
      await this.deviceUsageUtilsService.recalculateTotalUsageForDate(
        userId,
        sessionDate,
      );

    await this.deviceUsageUtilsService.updateMonthlyAverage(
      userId,
      sessionDate,
    );

    const resp = DeviceUsageSummaryDto.transform(updatedSummary, [session]);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device usage session updated and summary adjusted.',
      data: resp,
    };
  }

  //-----Analytics---------

  async getUserDeviceAnalytics(
    queryFilters: GetUserAnalyticsQueryInterface,
    userId: string,
  ): Promise<GetMonthlyUserDeviceUsageAvgRecordDTO> {
    const { filter } = queryFilters;

    switch (filter) {
      case AnalyticsRecordFilter.WEEKLY:
        return this.deviceUsageUtilsService.getWeeklyDeviceUsageRecords(userId);

      case AnalyticsRecordFilter.MONTHLY:
        return this.deviceUsageUtilsService.getMonthlyDeviceUsageRecords(
          userId,
        );

      case AnalyticsRecordFilter.HALF_YEARLY:
        return this.deviceUsageUtilsService.getHalfYearlyDeviceUsage(userId);

      case AnalyticsRecordFilter.YEARLY:
        return this.deviceUsageUtilsService.getYearlyDeviceUsage(userId);

      default:
        throw new BadRequestException('Invalid filter value');
    }
  }
}
