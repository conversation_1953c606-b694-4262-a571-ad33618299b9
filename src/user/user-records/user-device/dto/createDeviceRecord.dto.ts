import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, Min } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { DeviceUsageSessionDto } from './deviceUsageSession.dto';

export class CreateDeviceUsageSessionDto {
  @ApiProperty({
    example: '2025-04-22T08:30:00Z',
    description: 'Start time of the session in ISO format',
  })
  @IsNotEmpty()
  @IsDateString()
  startTime: string;

  @ApiProperty({
    example: 30,
    description: 'Duration set by user in minutes',
    minimum: 10,
    maximum: 60,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Duration must be at least 0 minutes' })
  durationSet: number;
}

export class CreateDeviceUsageRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Device usage record created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Created device usage record details',
    type: DeviceUsageSessionDto,
  })
  data: DeviceUsageSessionDto;
}
