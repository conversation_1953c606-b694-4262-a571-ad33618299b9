import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, Min } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { DeviceUsageSummaryDto } from './userDeviceRecords.dto';

export class AddMoreDeviceUsageSessionDto {
  @ApiProperty({
    example: '2025-04-22T08:30:00Z',
    description: 'Start time of the session in ISO format',
  })
  @IsNotEmpty()
  @IsDateString()
  startTime: string;

  @ApiProperty({
    example: 30,
    description: 'Duration set by user in minutes',
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Duration must be at least 0 minutes' })
  durationConsumed: number;
}

export class AddMoreDeviceUsageRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Device usage record added successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Added device usage record details',
    type: DeviceUsageSummaryDto,
  })
  data: DeviceUsageSummaryDto;
}
