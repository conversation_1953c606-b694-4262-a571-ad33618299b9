import { ApiProperty } from '@nestjs/swagger';
import {
  IsN<PERSON>ber,
  Min,
  IsNotEmpty,
  IsString,
  IsDateString,
  IsOptional,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class DeviceUsageAverageDTO {
  @ApiProperty({
    description: 'Year of the record',
    example: 2025,
  })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  year: number;

  @ApiProperty({
    description: 'Time period (e.g., first 10 days, second 10 days)',
  })
  @IsString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({
    description: 'Average device usage in the given month',
    example: 5.3,
  })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  averageDeviceUsage: number;

  @ApiProperty({
    description: 'Start date of the period (in ISO format, e.g., 2025-04-01)',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'End date of the period (in ISO format, e.g., 2025-04-10)',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  static transform(object: {
    year: number;
    period: string;
    averageDeviceUsage: number;
    startDate?: string;
    endDate?: string;
  }): DeviceUsageAverageDTO {
    const transformedObj = new DeviceUsageAverageDTO();
    transformedObj.year = object.year;
    transformedObj.period = object.period;
    transformedObj.averageDeviceUsage = object.averageDeviceUsage;
    transformedObj.startDate = object.startDate;
    transformedObj.endDate = object.endDate;
    return transformedObj;
  }
}
