import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { UserSleepRecordsDTO } from './userSleepRecord.dto';

export class GetUserSleepRecordDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of sleep records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'List of sleep records',
    type: [UserSleepRecordsDTO],
  })
  data: UserSleepRecordsDTO[];
}
