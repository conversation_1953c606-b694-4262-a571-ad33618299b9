import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  Min,
  IsNotEmpty,
  IsDateString,
  IsOptional,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class AverageSleepDTO {
  @ApiProperty({
    description: 'Year of the month, e.g., 2025',
  })
  @IsString()
  @IsNotEmpty()
  year: string;

  @ApiProperty({
    description: 'Time period (e.g., first 10 days, second 10 days)',
  })
  @IsString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({ description: 'Average sleep score for the time period' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  averageSleep: number;

  @ApiPropertyOptional({
    description: 'Start date of the period (in ISO format, e.g., 2025-04-01)',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date of the period (in ISO format, e.g., 2025-04-10)',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  static transform(object: {
    year: string;
    period: string;
    averageSleep: number;
    startDate?: string;
    endDate?: string;
  }): AverageSleepDTO {
    const transformedObj = new AverageSleepDTO();
    transformedObj.year = object.year;
    transformedObj.period = object.period;
    transformedObj.averageSleep = object.averageSleep;
    transformedObj.startDate = object.startDate;
    transformedObj.endDate = object.endDate;
    return transformedObj;
  }
}
