import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { AverageSleepDTO } from './averageSleep.dto';

export class GetUserSleepAvgRecordDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of sleep records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'List of sleep records',
    type: [AverageSleepDTO],
  })
  data: AverageSleepDTO[];
}
