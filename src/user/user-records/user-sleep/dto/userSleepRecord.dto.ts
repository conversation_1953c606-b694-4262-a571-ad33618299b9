import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  Min,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';

export class UserSleepRecordsDTO {
  @ApiProperty({ description: 'Unique identifier of the sleep record' })
  @IsMongoId()
  id: string;

  @ApiProperty({ description: 'User ID associated with the sleep record' })
  @IsMongoId()
  @IsNotEmpty()
  userId: Types.ObjectId;

  @ApiProperty({ description: 'Date of sleep record', example: '2025-03-25' })
  @IsDateString()
  @IsNotEmpty()
  date: string;

  @ApiProperty({ description: 'Number of hours slept' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  numOfHours: number;

  createdAt: Date;

  static transform(object: any): UserSleepRecordsDTO {
    const transformedObj = new UserSleepRecordsDTO();
    transformedObj.id = object._id.toString();
    transformedObj.userId = object.userId;
    transformedObj.numOfHours = object.numOfHours;
    transformedObj.date = object.date;

    transformedObj.createdAt = object.createdAt;
    return transformedObj;
  }
}
