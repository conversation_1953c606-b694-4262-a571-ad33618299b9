import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';
import { BaseResponse } from 'src/utils/responses';
import { UserSleepRecordsDTO } from './userSleepRecord.dto';

export class CreateSleepRecordReqDTO {
  @ApiProperty({ description: 'Number of hours slept', example: 7.5 })
  @IsNumber()
  @Min(0)
  @Max(24)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  numOfHours: number;

  @ApiProperty({ description: 'Date of sleep record', example: '2025-03-25' })
  @IsDateString({}, { message: 'Please select a valid date.' })
  @IsNotEmpty({ message: 'Please select a date.' })
  date: string;
}

export class CreateSleepRecordResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Sleep record created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Created sleep record details',
    type: UserSleepRecordsDTO,
  })
  data: UserSleepRecordsDTO;
}
