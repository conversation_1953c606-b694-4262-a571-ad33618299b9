import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Query,
} from '@nestjs/common';
import { Request } from 'express';
import { SleepRecordsService } from './user-sleep.service';
import {
  CreateSleepRecordReqDTO,
  CreateSleepRecordResDTO,
  GetUserSleepAvgRecordDTO,
  GetUserSleepRecordDTO,
} from './dto';
import { AuthGuard } from 'src/middlewares';
import { ApiTags, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';

import { GetUserSleepRecordsQueryInterface } from './interface';
import { GetUserAnalyticsQueryInterface } from 'src/common/interfaces';

@ApiTags('User Sleep Records')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class SleepRecordsController {
  constructor(private readonly sleepRecordsService: SleepRecordsService) {}

  @ApiResponse({
    status: 200,
    description: 'Sleep record created successfully',
    type: CreateSleepRecordResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('/sleep_records')
  async createSleepRecord(
    @Body() sleepData: CreateSleepRecordReqDTO,
    @Req() req: Request,
  ): Promise<CreateSleepRecordResDTO> {
    const user = req['user'];
    return this.sleepRecordsService.createSleepRecord(sleepData, user._id);
  }

  @Get('/sleep_records')
  async GetUserSleepRecords(
    @Req() req: Request,
    @Query() query: GetUserSleepRecordsQueryInterface,
  ) {
    const user = req['user'];
    return this.sleepRecordsService.getUserSleepRecords(query, user._id);
  }

  //   ---------------------------------------------------------------

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved sleep records',
    type: GetUserSleepRecordDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/sleep_analytics')
  async getUserSleepRecords(
    @Query() queryFilters: GetUserAnalyticsQueryInterface,
    @Req() req: Request,
  ): Promise<GetUserSleepAvgRecordDTO> {
    const user = req['user'];
    return this.sleepRecordsService.getUserSleeptAnalytics(
      queryFilters,
      user._id,
    );
  }
}
