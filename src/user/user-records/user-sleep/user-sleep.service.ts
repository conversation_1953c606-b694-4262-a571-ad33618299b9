import { Injectable, BadRequestException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as moment from 'moment-timezone';

import {
  CreateSleepRecordReqDTO,
  CreateSleepRecordResDTO,
  GetUserSleepAvgRecordDTO,
  UserSleepRecordsDTO,
  GetSingleUserSleepRecordsResDTO,
} from './dto';

import { SleepRecordUtilsService } from './user-sleep.utils.service';
import { UserSleepRecords } from 'models/user-records/Sleep-Records';
import { GetUserSleepRecordsQueryInterface } from './interface';
import {
  AnalyticsRecordFilter,
  GetUserAnalyticsQueryInterface,
} from 'src/common/interfaces';
import { User } from 'models/user';

@Injectable()
export class SleepRecordsService {
  constructor(
    @InjectModel(UserSleepRecords.name)
    private readonly sleepRecordModel: Model<UserSleepRecords>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    private readonly sleepRecordUtilsService: SleepRecordUtilsService,
  ) {}

  async createSleepRecord(
    sleepData: CreateSleepRecordReqDTO,
    userId: string,
  ): Promise<CreateSleepRecordResDTO> {
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const inputDateInTZ = moment
      .tz(sleepData.date, userTimeZone)
      .startOf('day');
    const todayInTZ = moment.tz(userTimeZone).startOf('day');

    if (inputDateInTZ.isAfter(todayInTZ)) {
      throw new BadRequestException(
        `You can only add sleep records for past dates. Future dates are not allowed.`,
      );
    }

    const startOfDayUTC = inputDateInTZ.clone().utc().toDate();
    const endOfDayUTC = inputDateInTZ.clone().endOf('day').utc().toDate();

    const existingRecord = await this.sleepRecordModel.findOne({
      userId,
      date: {
        $gte: startOfDayUTC,
        $lte: endOfDayUTC,
      },
    });

    if (existingRecord) {
      throw new BadRequestException(
        `You have already added your sleep record for ${inputDateInTZ.format('YYYY-MM-DD')} (${userTimeZone}).`,
      );
    }

    const newSleepRecord = await this.sleepRecordModel.create({
      ...sleepData,
      userId,
    });

    await this.sleepRecordUtilsService.updateMonthlyAverage(
      userId,
      inputDateInTZ.toDate(),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Sleep record created successfully !!',
      data: UserSleepRecordsDTO.transform(newSleepRecord),
    };
  }

  async getUserSleeptAnalytics(
    queryFilters: GetUserAnalyticsQueryInterface,
    userId: string,
  ): Promise<GetUserSleepAvgRecordDTO> {
    const { filter } = queryFilters;

    switch (filter) {
      case AnalyticsRecordFilter.WEEKLY:
        return this.sleepRecordUtilsService.getWeeklySleepRecords(userId);

      case AnalyticsRecordFilter.MONTHLY:
        return this.sleepRecordUtilsService.getMonthlySleepRecords(userId);

      case AnalyticsRecordFilter.HALF_YEARLY:
        return this.sleepRecordUtilsService.getHalfYearlySleepRecords(userId);

      case AnalyticsRecordFilter.YEARLY:
        return this.sleepRecordUtilsService.getYearlySleepRecords(userId);

      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }

  async getUserSleepRecords(
    queryFilter: GetUserSleepRecordsQueryInterface,
    userId: string,
  ): Promise<GetSingleUserSleepRecordsResDTO> {
    const { date } = queryFilter;

    if (date) {
      const record = await this.sleepRecordModel
        .findOne({ userId, date: date.trim() })
        .sort({ createdAt: -1 });

      if (!record) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          data: null,
        };
      }

      const resp = UserSleepRecordsDTO.transform(record);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: resp,
      };
    } else {
      return this.sleepRecordUtilsService.getLastSleepLogIn7Days(userId);
    }
  }
}
