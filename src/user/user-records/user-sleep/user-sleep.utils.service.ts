import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { UserSleepRecordsDTO } from './dto';
import { AverageSleepDTO } from './dto';
import { HttpStatus } from '@nestjs/common';
import {
  MonthlySleepSummary,
  UserSleepRecords,
} from 'models/user-records/Sleep-Records';
import { User } from 'models/user';
import * as moment from 'moment-timezone';

@Injectable()
export class SleepRecordUtilsService {
  constructor(
    @InjectModel(UserSleepRecords.name)
    private readonly sleepRecordModel: Model<UserSleepRecords>,
    @InjectModel(MonthlySleepSummary.name)
    private readonly monthlySleepSummaryModel: Model<MonthlySleepSummary>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  // Helper function to calculate average sleep
  private calculateAverageSleep(records: any[]): number | null {
    if (records.length === 0) return null;
    const totalSleepHours = records.reduce(
      (sum, record) => sum + record.numOfHours,
      0,
    );
    return Math.round(totalSleepHours / records.length);
  }

  // weekly sleep records (grouped into 7-day segments)
  async getWeeklySleepRecords(userId: string) {
    // 1. Get user time zone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // 2. Get today's date in user's TZ, and the last 6 days
    const todayInTZ = moment.tz(userTimeZone).startOf('day');
    const sevenDaysAgoInTZ = todayInTZ.clone().subtract(6, 'days');

    // 3. Convert range to UTC for DB query
    const startUTC = sevenDaysAgoInTZ.clone().utc().toDate();
    const endUTC = todayInTZ.clone().endOf('day').utc().toDate();

    const sleepRecords = await this.sleepRecordModel
      .find({
        userId,
        date: { $gte: startUTC, $lte: endUTC },
      })
      .lean();

    // 4. Group records by local date in user's time zone
    const groupedData: Record<string, any[]> = {};
    sleepRecords.forEach((record) => {
      const localDate = moment
        .utc(record.date)
        .tz(userTimeZone)
        .format('YYYY-MM-DD');
      if (!groupedData[localDate]) groupedData[localDate] = [];
      groupedData[localDate].push(record);
    });

    // 5. Generate 7 days of data
    const results = [];
    for (let i = 0; i < 7; i++) {
      const dateInTZ = sevenDaysAgoInTZ.clone().add(i, 'days');
      const dateKey = dateInTZ.format('YYYY-MM-DD');
      const records = groupedData[dateKey] ?? [];

      const avgSleep =
        records.length > 0
          ? records.reduce((sum, r) => sum + (r.numOfHours || 0), 0) /
            records.length
          : null;

      results.push(
        AverageSleepDTO.transform({
          year: dateInTZ.format('YYYY'),
          period: dateKey,
          averageSleep: avgSleep,
        }),
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: results.length,
      data: results,
    };
  }

  async getMonthlySleepRecords(userId: string) {
    // 1. Get user timezone
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    // 2. Get today and 30 days ago in user's time zone
    const todayInTZ = moment.tz(userTimeZone).startOf('day');
    const oneMonthAgoInTZ = todayInTZ.clone().subtract(30, 'days');

    // 3. Convert to UTC for DB query
    const oneMonthAgoUTC = oneMonthAgoInTZ.clone().utc().toDate();
    const monthEndDateUTC = todayInTZ.clone().endOf('day').utc().toDate();

    const sleepRecords = await this.sleepRecordModel
      .find({
        userId,
        date: { $gte: oneMonthAgoUTC, $lte: monthEndDateUTC },
      })
      .lean();

    // 4. Group into 5 weeks
    const groupedData: Record<string, any[]> = {};
    for (let i = 1; i <= 5; i++) {
      groupedData[`week${i}`] = [];
    }

    sleepRecords.forEach((record) => {
      // Convert date to user's TZ
      const recordDateInTZ = moment
        .utc(record.date)
        .tz(userTimeZone)
        .startOf('day');
      const diffInDays = recordDateInTZ.diff(oneMonthAgoInTZ, 'days');
      const weekIndex = Math.ceil((diffInDays + 1) / 7);
      if (weekIndex >= 1 && weekIndex <= 5) {
        groupedData[`week${weekIndex}`].push(record);
      }
    });

    const formatDate = (date: moment.Moment) => date.format('YYYY-MM-DD');

    // 5. Build response
    const responseRecords = Array.from({ length: 5 }, (_, index) => {
      const weekKey = `week${index + 1}`;
      const records = groupedData[weekKey];
      const avgSleep = this.calculateAverageSleep(records);

      const weekStartDate = oneMonthAgoInTZ.clone().add(index * 7, 'days');
      const weekEndDate = oneMonthAgoInTZ
        .clone()
        .add((index + 1) * 7 - 1, 'days');

      // Clamp to today's date if needed
      if (weekEndDate.isAfter(todayInTZ)) {
        weekEndDate.set({
          year: todayInTZ.year(),
          month: todayInTZ.month(),
          date: todayInTZ.date(),
        });
      }

      return AverageSleepDTO.transform({
        year: todayInTZ.format('YYYY'),
        period: `week_${index + 1}`,
        averageSleep: avgSleep ?? null,
        startDate: formatDate(weekStartDate),
        endDate: formatDate(weekEndDate),
      });
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  // Half-Yearly sleep records (grouped into 18-day segments)
  async getHalfYearlySleepRecords(userId: string) {
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const currentDate = moment.tz(userTimeZone);
    const recordsMap = new Map<number, any>();

    for (let i = 5; i >= 0; i--) {
      const monthDate = currentDate
        .clone()
        .subtract(i, 'months')
        .startOf('month');

      const year = monthDate.year();
      const month = monthDate.month() + 1; // month is 0-indexed
      const monthKey = year * 100 + month;

      const monthName = monthDate.format('MMMM');
      recordsMap.set(monthKey, {
        year: year.toString(),
        period: monthName,
        averageSleep: null,
      });
    }

    const keys = [...recordsMap.keys()];
    const records = await this.monthlySleepSummaryModel
      .find({
        userId,
        month: {
          $gte: keys[0],
          $lte: keys[keys.length - 1],
        },
      })
      .lean();

    records.forEach(({ month, year, averageSleep }) => {
      if (recordsMap.has(month)) {
        const paddedMonth = String(month % 100).padStart(2, '0');
        const dateStr = `${year}-${paddedMonth}-01`;
        const monthDate = moment.tz(dateStr, userTimeZone);
        const monthName = monthDate.format('MMMM');

        recordsMap.set(month, {
          year: year.toString(),
          period: monthName,
          averageSleep,
        });
      }
    });

    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      AverageSleepDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  // Yearly sleep records (grouped into 30-day segments)
  async getYearlySleepRecords(userId: string) {
    const currentDate = new Date();
    const recordsMap = new Map();

    // Generate keys for the last 12 months (including the current month)
    for (let i = 11; i >= 0; i--) {
      const date = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - i,
        1,
      );

      const monthKey = date.getFullYear() * 100 + (date.getMonth() + 1);
      const monthLabel = date.toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      });
      const [monthName, yearStr] = monthLabel.split(' ');

      recordsMap.set(monthKey, {
        year: yearStr,
        period: monthName,
        averageSleep: null, // Default to null (or 0 if you prefer)
      });
    }

    // Fetch stored monthly averages for the last 12 months
    const records = await this.monthlySleepSummaryModel
      .find({
        userId,
        month: {
          $gte: [...recordsMap.keys()][0],
          $lte: [...recordsMap.keys()][11],
        },
      })
      .lean();

    // Fill the recordsMap with actual data
    records.forEach(({ month, year, averageSleep }) => {
      if (recordsMap.has(month)) {
        recordsMap.set(month, {
          year: year.toString(),
          period: new Date(`${year}-${month % 100}-01`).toLocaleString(
            'default',
            { month: 'long' },
          ),
          averageSleep,
        });
      }
    });

    // Transform data using DTO and return response
    const responseRecords = Array.from(recordsMap.values()).map((record) =>
      AverageSleepDTO.transform(record),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async getLastSleepLogIn7Days(userId: string) {
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const startOfToday = moment.tz(userTimeZone).startOf('day').toDate();
    const endOfToday = moment.tz(userTimeZone).endOf('day').toDate();

    let records = await this.sleepRecordModel
      .find({ userId, date: { $gte: startOfToday, $lte: endOfToday } })
      .sort({ date: -1 });

    if (records.length === 0) {
      const sevenDaysAgo = moment
        .tz(userTimeZone)
        .subtract(7, 'days')
        .startOf('day')
        .toDate();

      records = await this.sleepRecordModel
        .find({ userId, date: { $gte: sevenDaysAgo, $lte: endOfToday } })
        .sort({ date: -1 });

      if (records.length === 0) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          data: null,
        };
      }
    }

    const lastDate = moment(records[0].date)
      .tz(userTimeZone)
      .startOf('day')
      .toDate();
    const nextDay = moment(lastDate).add(1, 'day').toDate();

    const sleepRecords = await this.sleepRecordModel
      .find({ userId, date: { $gte: lastDate, $lt: nextDay } })
      .sort({ createdAt: -1 })
      .exec();

    const resp = UserSleepRecordsDTO.transform(sleepRecords[0]);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }

  /**
   * Updates the monthly average when a sleep record is created or updated.
   */
  async updateMonthlyAverage(userId: string, date: Date) {
    const user = await this.userModel.findById(userId);
    const userTimeZone = user?.timeZone || 'UTC';

    const monthMoment = moment(date).tz(userTimeZone).startOf('month');

    const year = monthMoment.year();
    const month = monthMoment.month() + 1; // moment.js months are 0-indexed
    const monthKey = year * 100 + month;

    const startOfMonth = monthMoment.clone().startOf('month').toDate();
    const endOfMonth = monthMoment.clone().endOf('month').toDate();

    const monthlyRecords = await this.sleepRecordModel.find({
      userId,
      date: { $gte: startOfMonth, $lte: endOfMonth },
    });

    const averageSleep = this.calculateAverageSleep(monthlyRecords);

    await this.monthlySleepSummaryModel.findOneAndUpdate(
      { userId, month: monthKey },
      { userId, month: monthKey, year, averageSleep },
      { upsert: true, new: true },
    );
  }
}
