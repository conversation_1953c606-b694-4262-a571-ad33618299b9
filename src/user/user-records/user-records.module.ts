import { Module } from '@nestjs/common';
import { UserMoodController } from './user-mood/user-mood.controller';
import { UserMoodService } from './user-mood/user-mood.service';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { SleepRecordsController } from './user-sleep/user-sleep.controller';
import { SleepRecordsService } from './user-sleep/user-sleep.service';
import { UserweightController } from './user-weight/user-weight.controller';
import { UserWeightService } from './user-weight/user-weight.service';
import { SleepRecordUtilsService } from './user-sleep/user-sleep.utils.service';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { UserMealController } from './user-meals/user-meals.controller';
import { UserMealService } from './user-meals/user-meals.service';
import { UserMealRecordsUtilsService } from './user-meals/user-meals-utils.service';
import { UserMoodUtilsService } from './user-mood/user-mood-utils.service';
import { WeightRecordUtilsService } from './user-weight/user-weight.utils.service';

import {
  MealDailyAverageRecords,
  MealDailyAverageRecordsSchema,
  MealDailyTotalRecord,
  MealDailyTotalRecordSchema,
  MealmonthlyAverageRecords,
  MealmonthlyAverageRecordsSchema,
  UserMealRecords,
  UserMealRecordsSchema,
} from 'models/user-records/Meal-Records';
import {
  MonthlySleepSummary,
  MonthlySleepSummarySchema,
  UserSleepRecords,
  UserSleepRecordsSchema,
} from 'models/user-records/Sleep-Records';
import {
  UserMoodRecords,
  UserMoodRecordsSchema,
} from 'models/user-records/Mood-Records';
import {
  DailyMoodAverage,
  DailyMoodSchema,
} from 'models/user-records/Mood-Records/userMoodDailyAvg.schema';

import {
  MonthlyMoodAverage,
  MonthlyMoodSchema,
} from 'models/user-records/Mood-Records/userMoodMonthlyAvg.schema';
import {
  MonthlyWeightSummary,
  MonthlyWeightSummarySchema,
  UserWeightRecords,
  UserWeightRecordsSchema,
} from 'models/user-records/Weight-Records';
import { DeviceUsageController } from './user-device/user-device.controller';
import { DeviceUsageService } from './user-device/user-device.service';
import {
  DeviceUsageSession,
  DeviceUsageSessionSchema,
  UserDeviceRecord,
  UserDeviceRecordSchema,
  MonthlyDeviceUsageSummary,
  MonthlyDeviceUsageSummarySchema,
} from 'models/user-records/Device-records';
import { DeviceUsageUtilsService } from './user-device/user-device.utils.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserMoodRecords.name, schema: UserMoodRecordsSchema },
      { name: DailyMoodAverage.name, schema: DailyMoodSchema },
      { name: MonthlyMoodAverage.name, schema: MonthlyMoodSchema },

      { name: UserSleepRecords.name, schema: UserSleepRecordsSchema },
      { name: MonthlySleepSummary.name, schema: MonthlySleepSummarySchema },

      { name: UserWeightRecords.name, schema: UserWeightRecordsSchema },
      { name: MonthlyWeightSummary.name, schema: MonthlyWeightSummarySchema },

      { name: UserMealRecords.name, schema: UserMealRecordsSchema },
      {
        name: MealDailyAverageRecords.name,
        schema: MealDailyAverageRecordsSchema,
      },
      {
        name: MealDailyTotalRecord.name,
        schema: MealDailyTotalRecordSchema,
      },
      {
        name: MealmonthlyAverageRecords.name,
        schema: MealmonthlyAverageRecordsSchema,
      },
      {
        name: DeviceUsageSession.name,
        schema: DeviceUsageSessionSchema,
      },
      {
        name: UserDeviceRecord.name,
        schema: UserDeviceRecordSchema,
      },
      {
        name: MonthlyDeviceUsageSummary.name,
        schema: MonthlyDeviceUsageSummarySchema,
      },
    ]),
    ThirdPartyModule,
    CommonModule,
    AuthModule,
    RepoModule,
  ],
  controllers: [
    UserMoodController,
    SleepRecordsController,
    UserweightController,
    UserMealController,
    DeviceUsageController,
  ],
  providers: [
    UserMoodService,

    SleepRecordsService,
    SleepRecordUtilsService,

    UserWeightService,
    WeightRecordUtilsService,

    UserMealService,
    UserMealRecordsUtilsService,
    UserMoodUtilsService,
    DeviceUsageService,
    DeviceUsageUtilsService,
  ],
})
export class UserRecordsModule {}
