import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Activity } from 'models/activity';
import { getAllActivityVideosQueryInterface } from './interfaces';
import { UtilsService } from 'src/common/services';
import {
  ActivityVideoDto,
  GetAllActivityVideoLibraryResDTO,
  GetSingleActivityVideoLibraryResDTO,
  MoodVideoRecommendationResDTO,
  RecommendedVideoDTO,
} from './dto';
import { MoodMedia } from 'models/mood';
import { OpenAIService } from 'src/third-party/openAI/openai.service';
import { MoodUtilsService } from './mood-video-recommandation.utils.service';
import { UserMoodRecords } from 'models/user-records/Mood-Records';

@Injectable()
export class VideoLibraryService {
  constructor(
    @InjectModel(Activity.name) private readonly activityModel: Model<Activity>,
    @InjectModel(MoodMedia.name)
    private readonly moodMediaModel: Model<MoodMedia>,
    @InjectModel(UserMoodRecords.name)
    private readonly userMoodRecordsModel: Model<UserMoodRecords>,

    private readonly openAIService: OpenAIService,

    private readonly utilsService: UtilsService,
    private readonly moodUtilsService: MoodUtilsService,
  ) {}

  async getAllActivityVideosForUser(
    queryFilters: getAllActivityVideosQueryInterface,
  ): Promise<GetAllActivityVideoLibraryResDTO> {
    const { page, tag, filter } = queryFilters;
    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);
    const baseQuery: any = { isDeleted: false };

    let videos;
    let total;

    if (tag === 'wellness') {
      videos = await this.moodMediaModel
        .find(baseQuery)
        .skip(offset)
        .limit(limit)
        .exec();
      total = await this.moodMediaModel.countDocuments(baseQuery);
    } else if (!tag || tag === 'workout') {
      const workoutQuery = { ...baseQuery };

      if (filter) {
        workoutQuery.subTags = filter; // Matches enum to DB field
      }

      videos = await this.activityModel
        .find(workoutQuery)
        .skip(offset)
        .limit(limit)
        .exec();
      total = await this.activityModel.countDocuments(workoutQuery);
    } else {
      throw new BadRequestException(
        'Invalid tag value. Allowed values: workout, wellness',
      );
    }

    const resp = videos.map((item) => ActivityVideoDto.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: videos.length,
      videos: resp,
    };
  }

  async getSingleActivityVideoByIdForUser(
    id: string,
  ): Promise<GetSingleActivityVideoLibraryResDTO> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException(`Invalid Video ID: ${id}`);
    }

    // Check in activityModel
    let video: Activity | MoodMedia | null = await this.activityModel
      .findById(id)
      .exec();
    let resp;

    // If not found in activityModel, check in moodMediaModel
    if (!video) {
      video = await this.moodMediaModel.findById(id).exec();
      if (video) {
        resp = RecommendedVideoDTO.transform(video as MoodMedia);
      }
    } else {
      resp = ActivityVideoDto.transform(video as Activity);
    }

    // If still not found, throw error
    if (!video) {
      throw new NotFoundException(`Video with ID ${id} not found`);
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      video: resp,
    };
  }

  async getMoodVideoRecommendation(
    userId: string,
  ): Promise<MoodVideoRecommendationResDTO> {
    const past24Hours = new Date();
    past24Hours.setHours(past24Hours.getHours() - 24);

    const moodRecords = await this.userMoodRecordsModel.find({
      userId,
      createdAt: { $gte: past24Hours, $lt: new Date() },
    });

    const { moodType, hungerLevel } =
      await this.moodUtilsService.analyzeMood(moodRecords);

    console.log('moodType', moodType);
    console.log('hungerLevel', hungerLevel);

    const videos = await this.moodMediaModel.find({
      moodType,
      hungerLevel,
      isDeleted: false,
    });

    const recommendedVideo = this.moodUtilsService.getRecommendedVideo(
      userId,
      videos,
    );

    return {
      error: false,
      statusCode: 200,
      data: RecommendedVideoDTO.transform(recommendedVideo),
    };
  }

  async getActivityVideoRecommendation(
    userId: string,
  ): Promise<GetSingleActivityVideoLibraryResDTO> {
    const query: any = { isDeleted: false };

    const totalVideos = await this.activityModel.countDocuments(query);

    if (!totalVideos) {
      throw new NotFoundException('No video found');
    }

    // Compute index based on current hour + userId hash
    const currentHour = new Date().getHours();

    // Create a simple hash of the userId to add user-based variation
    const userHash = this.hashUserId(userId);

    const index = (currentHour + userHash) % totalVideos;

    const video = await this.activityModel
      .find(query)
      .skip(index)
      .limit(1)
      .exec();

    const resp = video.map((item) => ActivityVideoDto.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      video: resp[0],
    };
  }

  // Utility function to generate a numeric hash from userId
  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = (hash << 5) - hash + userId.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }
}
