import { Injectable, Inject, Logger, NotFoundException } from '@nestjs/common';
import { OpenAIService } from 'src/third-party/openAI/openai.service';
import * as crypto from 'crypto';

@Injectable()
export class MoodUtilsService {
  private readonly logger = new Logger(MoodUtilsService.name);

  constructor(@Inject(OpenAIService) private openAIService: OpenAIService) {}

  async analyzeMood(
    userMoodRecords: any[],
  ): Promise<{ moodType: string; hungerLevel: string }> {
    if (userMoodRecords.length === 0) {
      return { moodType: 'happy', hungerLevel: 'mild' };
    }

    const moodSummary = userMoodRecords
      .map(
        (record) => `Mood: ${record.moodType}, Hunger: ${record.hungerLevel}`,
      )
      .join('; ');

    const basePrompt = process.env.MOOD_VIDEO_RECOMMANDATION_PROMPT;
    const prompt = basePrompt.replace('{moodRecords}', moodSummary);

    try {
      const aiResponse = await this.openAIService.generateResponse(prompt);
      this.logger.log(`AI Response: ${aiResponse}`);

      const match = aiResponse.match(/Mood: (.*?); Hunger: (.*?)[\.\n]?$/);

      if (match) {
        return { moodType: match[1].trim(), hungerLevel: match[2].trim() };
      } else {
        throw new Error('Invalid AI response format');
      }
    } catch (error) {
      this.logger.error('OpenAI failed, using fallback logic:', error);

      return this.getFallbackMood(userMoodRecords);
    }
  }

  private getFallbackMood(userMoodRecords: any[]): {
    moodType: string;
    hungerLevel: string;
  } {
    const MOOD_SCORES: Record<string, number> = {
      sad: 1,
      anxious: 2,
      irritated: 3,
      'moderately happy': 4,
      happy: 5,
    };

    const HUNGER_SCORES: Record<string, number> = {
      mild: 1,
      barely: 2,
      moderate: 3,
      high: 4,
    };

    let totalMoodScore = 0;
    let totalHungerScore = 0;
    const totalEntries = userMoodRecords.length;

    userMoodRecords.forEach(({ moodType, hungerLevel }) => {
      totalMoodScore += MOOD_SCORES[moodType] || 0;
      totalHungerScore += HUNGER_SCORES[hungerLevel] || 0;
    });

    const avgMoodScore = totalMoodScore / totalEntries;
    const avgHungerScore = totalHungerScore / totalEntries;

    const moodType = Object.keys(MOOD_SCORES).reduce((a, b) =>
      Math.abs(MOOD_SCORES[a] - avgMoodScore) <
      Math.abs(MOOD_SCORES[b] - avgMoodScore)
        ? a
        : b,
    );

    const hungerLevel = Object.keys(HUNGER_SCORES).reduce((a, b) =>
      Math.abs(HUNGER_SCORES[a] - avgHungerScore) <
      Math.abs(HUNGER_SCORES[b] - avgHungerScore)
        ? a
        : b,
    );

    return { moodType, hungerLevel };
  }

  getRecommendedVideo(userId: string, videos: any[]): any {
    if (videos.length === 0) {
      throw new NotFoundException('No recommended videos found.');
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dateString = today.toISOString().split('T')[0];

    const seed = crypto
      .createHash('sha256')
      .update(userId + dateString)
      .digest('hex');
    const seedNumber = parseInt(seed.substring(0, 8), 16);

    const randomIndex = seedNumber % videos.length;

    return videos[randomIndex];
  }
}
