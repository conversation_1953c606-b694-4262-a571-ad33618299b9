import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Activity, ActivitySchema } from 'models/activity';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RepoModule } from 'src/repo/repo.module';
import { VideoLibraryService } from './video-library.service';
import { VideoLibraryController } from './video-library.controller';
import { MoodMedia, MoodMediaSchema } from 'models/mood';
import {
  UserMoodRecords,
  UserMoodRecordsSchema,
} from 'models/user-records/Mood-Records';
import { OpenAIService } from 'src/third-party/openAI/openai.service';
import { MoodUtilsService } from './mood-video-recommandation.utils.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Activity.name, schema: ActivitySchema },
      { name: MoodMedia.name, schema: MoodMediaSchema },
      { name: UserMoodRecords.name, schema: UserMoodRecordsSchema },
    ]),
    AuthModule,
    RepoModule,
    CommonModule,
  ],
  controllers: [VideoLibraryController],
  providers: [VideoLibraryService, OpenAIService, MoodUtilsService],
})
export class VideoLibraryModule {}
