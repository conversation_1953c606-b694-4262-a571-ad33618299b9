import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { RecommendedVideoDTO } from './moodVideoRecommandation.dto';
import { IsObject } from 'class-validator';

export class MoodVideoRecommendationResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Recommended video details',
    type: RecommendedVideoDTO,
  })
  @IsObject()
  data: RecommendedVideoDTO;
}
