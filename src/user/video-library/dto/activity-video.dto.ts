import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';
import { Activity } from 'models/activity/activity.schema';

export class ActivityVideoDto {
  @ApiProperty({ example: '67a20c327b4206cda53747cd' })
  id: string;

  @ApiProperty({ example: 'Sample Activity Title' })
  @IsString()
  title: string;

  @ApiProperty({ example: 'Sample Activity Description' })
  @IsString()
  description: string;

  @ApiProperty({ example: false })
  @IsBoolean()
  isPublished: boolean;

  @ApiProperty({ example: 30 })
  @IsNumber()
  completionTime: number;

  @ApiProperty({ example: 'wellness' })
  @IsString()
  tag: string;

  @ApiPropertyOptional({ example: ['stretch', 'yoga'], type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  subTags?: string[];

  @ApiProperty({ example: 2500 })
  @IsNumber()
  calorieEstimate: number;

  @ApiPropertyOptional({
    example: 'https://www.youtube.com/watch?v=S-ww4ZENJBE',
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({ example: '2025-02-04T12:46:43.003Z' })
  @IsString()
  createdAt: string;

  static transform(object: Activity): ActivityVideoDto {
    const transformedObj: ActivityVideoDto = new ActivityVideoDto();

    transformedObj.id = object._id.toString();
    transformedObj.title = object.title;
    transformedObj.description = object.description;
    transformedObj.isPublished = object.isPublished;
    transformedObj.completionTime = object.completionTime;
    transformedObj.tag = object.tag;
    transformedObj.subTags = object.subTags;
    transformedObj.calorieEstimate = object.calorieEstimate;
    transformedObj.videoUrl = object.videoUrl;
    transformedObj.createdAt = (object as any).createdAt;

    return transformedObj;
  }
}
