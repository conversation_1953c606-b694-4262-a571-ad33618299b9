import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { ActivityVideoDto } from './activity-video.dto';

export class GetAllActivityVideoLibraryResDTO extends BaseResponse {
  @ApiProperty({ example: 100, description: 'Total number of activity videos' })
  total: number;

  @ApiProperty({
    example: 50,
    description: 'Number of hits matching the query',
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of activity videos',
    type: [ActivityVideoDto],
  })
  videos: ActivityVideoDto[];
}
