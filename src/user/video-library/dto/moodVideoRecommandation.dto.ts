import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';
import { HUNGER_LEVELS, MOOD_TYPES } from 'models/user-records/Mood-Records';
import { MoodMedia } from 'models/mood';

export class RecommendedVideoDTO {
  @ApiProperty({
    description: 'Title of the video',
    example: 'Relaxing Meditation',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Description of the video',
    example: 'A calming video to boost your mood.',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'URL of the video',
    example: 'https://example.com/video.mp4',
  })
  @IsString()
  videoUrl: string;

  @ApiProperty({
    description: 'Mood type of the recommended video',
    enum: MOOD_TYPES,
    example: MOOD_TYPES.HAPPY,
  })
  @IsEnum(MOOD_TYPES)
  moodType: MOOD_TYPES;

  @ApiProperty({
    description: 'Hunger level associated with the video',
    enum: HUNGER_LEVELS,
    example: HUNGER_LEVELS.MODERATE,
  })
  @IsEnum(HUNGER_LEVELS)
  hungerLevel: HUNGER_LEVELS;

  static transform(object: MoodMedia): RecommendedVideoDTO {
    const transformedObj = new RecommendedVideoDTO();
    transformedObj.title = object.title;
    transformedObj.description = object.description;
    transformedObj.videoUrl = object.videoUrl;
    transformedObj.moodType = object.moodType as MOOD_TYPES;
    transformedObj.hungerLevel = object.hungerLevel as HUNGER_LEVELS;
    return transformedObj;
  }
}
