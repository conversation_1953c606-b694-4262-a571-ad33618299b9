import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { VideoLibraryService } from './video-library.service';
import { getAllActivityVideosQueryInterface } from './interfaces';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  GetAllActivityVideoLibraryResDTO,
  GetSingleActivityVideoLibraryResDTO,
  MoodVideoRecommendationResDTO,
} from './dto';
import { ErrorResponse } from 'src/utils/responses';
import { Request } from 'express';

@ApiTags('User-Video-Library')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class VideoLibraryController {
  constructor(private readonly videoLibraryService: VideoLibraryService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the videos list.',
    type: GetAllActivityVideoLibraryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('video_library')
  GetAllActivityVideosForUser(
    @Query() queryFilters: getAllActivityVideosQueryInterface,
  ) {
    return this.videoLibraryService.getAllActivityVideosForUser(queryFilters);
  }

  @ApiResponse({
    status: 200,
    description: 'Mood-based video recommendation retrieved successfully.',
    type: MoodVideoRecommendationResDTO,
  })
  @Get('mood_recommendation')
  async getMoodVideoRecommendation(
    @Req() req: Request,
  ): Promise<MoodVideoRecommendationResDTO> {
    const user = req['user'];

    return this.videoLibraryService.getMoodVideoRecommendation(user._id);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the single video data.',
    type: GetSingleActivityVideoLibraryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('video_library/:id')
  GetSingleActivityVideoById(@Param('id') id: string) {
    return this.videoLibraryService.getSingleActivityVideoByIdForUser(id);
  }

  @ApiResponse({
    status: 200,
    description: 'Activity video recommendation retrieved successfully.',
    type: GetSingleActivityVideoLibraryResDTO,
  })
  @Get('activity_recommendation')
  async getActivityVideoRecommendation(
    @Req() req: Request,
  ): Promise<GetSingleActivityVideoLibraryResDTO> {
    const user = req['user'];
    return this.videoLibraryService.getActivityVideoRecommendation(user._id);
  }
}
