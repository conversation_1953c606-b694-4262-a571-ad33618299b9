// Custom validator for subTags
import { BadRequestException } from '@nestjs/common';
import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { ACTIVITY_TAGS, TagSubTagMap } from 'models/activity';

@ValidatorConstraint({ name: 'validSubTags', async: false })
export class SubTagValidator implements ValidatorConstraintInterface {
  validate(subTags: string[], args: ValidationArguments) {
    if (!subTags || subTags.length === 0) {
      throw new BadRequestException('Please Provide SubTags !!');
    }

    const tag = (args.object as any).tag as ACTIVITY_TAGS;

    const allowedSubTags = TagSubTagMap[tag] || [];

    return subTags.every((subTag) => allowedSubTags.includes(subTag));
  }

  defaultMessage(args: ValidationArguments) {
    const tag = (args.object as any).tag as ACTIVITY_TAGS;
    return `Invalid subTags for tag "${tag}". Allowed: ${TagSubTagMap[tag]?.join(', ') || 'None'}`;
  }
}
