export const reminderMessages = {
  food: [
    'Time to fuel up! Enjoy your meal.',
    "Don't skip your meal! Your body needs energy.",
    'Delicious moments await you. Have a great meal!',
    'A well-fed body is a happy body. Bon appétit!',
    'Stay healthy, stay strong! Eat well.',
    'Your meal is calling! Take a break and enjoy.',
    "Food time is happiness time! Don't miss it.",
    'Every bite counts! Enjoy your food mindfully.',
    'Time to taste something amazing. Eat well!',
    'Healthy food, happy mood! Grab your meal.',
  ],
  workout: [
    'Time to get moving! Your body will thank you.',
    'Sweat now, shine later! Hit your workout.',
    "Stay fit, stay strong! It's workout time.",
    'Push your limits! You got this.',
    'No excuses! A little progress is still progress.',
    'Your body is your temple—take care of it.',
    'Exercise not only changes your body but also your mind.',
    'Consistency is key! Get that workout in.',
    'The hardest step is the first one—get started!',
    'Stronger every day! Time to work out.',
  ],
  device: [
    'Time to check your device status!',
    'Keep your devices running smoothly—stay updated!',
    'Device check-in time! Ensure everything is working.',
    'A well-maintained device means fewer problems later.',
    'Check your device now to avoid interruptions!',
    'Keep your tech in top shape—inspect your device.',
    'Regular maintenance keeps your device efficient.',
    'Donot forget to monitor your connected devices.',
    'Tech check! Ensure your device is functioning well.',
    'Stay ahead—check your device performance today!',
  ],
};

export const getRandomReminderMessage = (category: string) => {
  const messages = reminderMessages[category.toLowerCase()] || [];

  return messages.length > 0
    ? messages[Math.floor(Math.random() * messages.length)]
    : "Don't forget your reminder!";
};

export const getPageUrl = (
  category: 'food' | 'workout' | 'device',
  subCategory: string,
) => {
  if (category === 'food') {
    return `/food-reminder?meal=${encodeURIComponent(subCategory)}`;
  } else if (category === 'workout') {
    return `/workout-reminder?exercise=${encodeURIComponent(subCategory)}`;
  } else {
    return `/device-reminder?device=${encodeURIComponent(subCategory)}`;
  }
};

export const dummyReminderMessages = [
  'Relax and recharge—use your device for a soothing massage today!',
  'Your massage device is ready—take a moment for yourself!',
  'Ease your stress—let your device help you unwind with a relaxing massage!',
  'Give your body the care it deserves—start a massage session with your device!',
  'Refresh your mind and body—your massage device is waiting for you!',
];
