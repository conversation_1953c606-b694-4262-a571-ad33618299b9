import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import {
  Reminders_Sound,
  Reminders_SoundSchema,
  ReminderTime,
  ReminderTimeSchema,
} from 'models/reminders';
import { Document } from 'mongoose';

export enum NOTIFICATION_MESSAGE_STATUS {
  PENDING = 'pending',
  FAILED = 'failed',
  SUCCESS = 'success',
  IS_MISSED = 'is_missed',
}

@Schema({ timestamps: true })
export class NotificationMessage extends Document {
  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: String, required: true })
  notificationTokenId: string;

  @Prop({ type: String, required: true })
  reminderSettingsId: string;

  @Prop({
    type: String,
    enum: NOTIFICATION_MESSAGE_STATUS,
    default: NOTIFICATION_MESSAGE_STATUS.PENDING,
  })
  status: string;

  @Prop({ type: Boolean, default: false })
  isRead: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  //   ---------------------------------------

  @Prop({ type: Reminders_SoundSchema, required: true })
  sound: Reminders_Sound;

  @Prop({ type: String, required: true })
  label: string;

  @Prop({ type: String, required: true })
  category: string;

  @Prop({ type: String, required: true })
  frontend_screen_url: string;

  @Prop({ type: String, required: true })
  messageTitle: string;

  @Prop({ type: String, required: true })
  messageBody: string;

  @Prop({ type: ReminderTimeSchema, required: true })
  GMT_Time: ReminderTime;

  @Prop({ type: Boolean, default: false })
  isDummyReminder: boolean;

  @Prop({ type: Date })
  scheduledAt: Date;

  @Prop({ default: false })
  isQueued: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const NotificationMessageSchema =
  SchemaFactory.createForClass(NotificationMessage);

NotificationMessageSchema.index(
  {
    userId: 1,
    notificationTokenId: 1,
    reminderSettingsId: 1,
    category: 1,
    label: 1,
    isDummyReminder: 1,
    status: 1,
    isDeleted: 1,
  },
  {
    unique: true,
    partialFilterExpression: {
      status: NOTIFICATION_MESSAGE_STATUS.PENDING,
      isDeleted: false,
    },
  },
);
