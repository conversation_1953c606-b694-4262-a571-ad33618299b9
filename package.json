{"name": "appetec-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.738.0", "@aws-sdk/client-ses": "^3.731.1", "@aws-sdk/lib-storage": "^3.738.0", "@aws-sdk/s3-request-presigner": "^3.835.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/serve-static": "^5.0.1", "@nestjs/swagger": "^11.0.3", "@nestjs/throttler": "^6.4.0", "argon2": "^0.41.1", "axios": "^1.8.4", "clamav.js": "^0.12.0", "class-transform": "^0.7.0-dev.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cron": "^4.1.0", "crypto-js": "^4.2.0", "csurf": "^1.10.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "deps": "^1.0.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "firebase-admin": "^13.2.0", "ioredis": "^5.4.2", "json2csv": "^6.0.0-alpha.2", "luxon": "^3.6.1", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mongoose": "^8.9.5", "multer": "^1.4.5-lts.1", "openai": "^4.90.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "serve-static": "^1.16.2", "stomp-client": "^0.9.0", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.5", "validator": "^13.12.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/crypto-js": "^4.2.2", "@types/csurf": "^1.11.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/json2csv": "^5.0.7", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}